# Options Greeks Convergence Analysis

## Overview

This project recreates and analyzes the convergence patterns described in "The Convergence Points" document using options data for any ticker (SPX, VIX, TLT, etc.). The analysis identifies bearish, bullish, and explosive convergence patterns based on combinations of options Greeks including <PERSON>rm, <PERSON><PERSON>, GEX (Gamma Exposure), Vomma, VolmBS, and VxOI.

## Ticker Configuration

The system is ticker-agnostic and can analyze any options instrument. To change the ticker:

1. **Edit `config/config.py`**: Change the `TICKER` variable to your desired symbol (e.g., "VIX", "TLT")
2. **Data Files**: Ensure your data files follow the naming convention: `{ticker}_complete_{year}_{quarter}.csv`
3. **Column Names**: The system automatically detects price columns using patterns like `{ticker}_close`, `close`, `underlying_price`, etc.

## Project Structure

```
options_convergence_analysis/
├── src/                          # Source code modules
│   ├── __init__.py              # Package initialization
│   ├── main_analysis.py         # Main analysis orchestrator
│   ├── data_loader.py           # Data loading and preprocessing
│   ├── analytics_engine.py      # Greeks calculation engine
│   ├── convergence_processor.py # Convergence pattern analysis
│   ├── visualization.py         # Chart generation
│   └── report_generator.py      # PDF report generation
├── data/                        # Data files
│   ├── {ticker}_complete_2025_q2.csv # Options data (SPX, VIX, TLT, etc.)
│   └── convergence_analysis.txt # Document analysis
├── reports/                     # Generated outputs
│   ├── *.png                   # Generated charts
│   ├── *.pdf                   # Analysis report
│   └── *.csv                   # Data exports
├── config/                      # Configuration
│   └── config.py               # Project configuration
├── requirements.txt             # Python dependencies
└── README.md                   # This file
```

## Features

### Analytics Engine
- **Black-Scholes Implementation**: Complete options pricing model
- **First-Order Greeks**: Delta, Vega, Theta, Rho calculation
- **Second-Order Greeks**: Gamma, Vomma, Vanna, Charm calculation
- **Portfolio Aggregation**: Portfolio-level Greeks calculation
- **GEX Calculation**: Gamma Exposure by strike and total

### Convergence Analysis
- **Pattern Recognition**: Identifies bearish, bullish, and explosive patterns
- **Signal Generation**: Quantitative signals based on Greeks combinations
- **Historical Analysis**: Analysis of convergence dates from document
- **Predictive Framework**: Forward-looking convergence projections

### Visualization
- **Convergence Overview**: Multi-panel chart showing all Greeks
- **Greeks Heatmap**: Correlation and value analysis
- **Pattern Analysis**: Signal strength and pattern components
- **Summary Dashboard**: Key metrics and statistics

### Report Generation
- **Comprehensive PDF**: Professional analysis report
- **Methodology Section**: Detailed explanation of approach
- **Data Analysis**: Statistical summaries and insights
- **Visual Integration**: Charts embedded in report
- **Conclusions**: Key findings and recommendations

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Run the analysis:
```bash
cd src
python main_analysis.py
```

## Dependencies

- pandas>=2.0.0 - Data manipulation
- numpy>=1.24.0 - Numerical computing
- scipy>=1.10.0 - Scientific computing
- matplotlib>=3.7.0 - Plotting
- seaborn>=0.12.0 - Statistical visualization
- plotly>=5.14.0 - Interactive charts
- reportlab>=4.0.0 - PDF generation
- yfinance>=0.2.0 - Financial data
- python-dateutil>=2.8.0 - Date utilities

## Convergence Patterns

### Bearish Convergence Recipe
- High positive Charm (forced hedging)
- High positive GEX (resistance)
- Any Vomma level
- **Expected Outcome**: SPX NEGATIVE

### Bullish Convergence Recipe
- Negative Vomma (volatility crush)
- High Vanna (vol→delta conversion)
- Low/negative GEX (amplification)
- **Expected Outcome**: SPX POSITIVE

### Explosive Convergence Recipe
- Extreme negative Vomma (<-100M)
- Extreme Vanna (>200K)
- Moderate positive GEX
- Negative Charm
- **Expected Outcome**: SPX EXPLOSIVE POSITIVE

## Output Files

### Charts
- `convergence_overview.png` - Main convergence analysis chart
- `greeks_heatmap.png` - Greeks correlation heatmap
- `pattern_analysis.png` - Pattern component analysis
- `summary_dashboard.png` - Key metrics dashboard

### Reports
- `SPX_Convergence_Analysis_*.pdf` - Comprehensive analysis report
- `convergence_data.csv` - Detailed convergence metrics
- `summary_statistics.csv` - Summary statistics

## Key Metrics

The analysis calculates and tracks:
- **Charm**: Time decay of Delta (thousands)
- **Vanna**: Cross-derivative Delta/Volatility (thousands)
- **GEX**: Gamma Exposure (millions)
- **Vomma**: Volatility Gamma (millions)
- **VolmBS**: Volume-weighted flow (thousands)
- **VxOI**: Volatility × Open Interest (millions)

## Usage Notes

1. **Data Requirements**: Requires SPX options data with strikes, expiries, prices, and basic Greeks
2. **Date Matching**: Analysis attempts to find nearby dates if exact convergence dates are unavailable
3. **Scaling**: Greeks are scaled to match document conventions (thousands/millions)
4. **Signal Strength**: Combined signal strength indicates overall convergence intensity

## Risk Disclaimer

This analysis is for educational and research purposes only. Options trading involves substantial risk and may not be suitable for all investors. Past performance does not guarantee future results. Market conditions can change rapidly, affecting the reliability of Greeks-based predictions.

## Author

SPX Analytics Team
Generated: June 26, 2025

