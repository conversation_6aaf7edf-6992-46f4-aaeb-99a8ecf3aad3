analysis_type,date,current_price,total_contracts,total_open_interest,total_gamma_exposure,atm_strikes,high_oi_strikes,pin_risk_strikes,convergence_pressure,days_to_target,gamma_exposure,expected_analysis_type,expected_primary_greek
FORWARD_LOOKING,2025-06-30,6092.16,942,1108876,697.69,"[5805.0, 5810.0, 5815.0, 5820.0, 5825.0, 5830.0, 5835.0, 5840.0, 5845.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6285.0, 6290.0, 6295.0, 6300.0, 6305.0, 6310.0, 6315.0, 6320.0, 6325.0, 6330.0, 6335.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6390.0, 6400.0, 6410.0]","[4800.0, 5905.0, 4850.0, 4480.0, 5310.0, 6000.0, 5900.0, 5000.0, 5800.0, 5200.0]","{4800.0: {'open_interest': 56086, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(21.21), 'pin_strength': np.float64(264429.24)}, 5905.0: {'open_interest': 55612, 'gamma_exposure': np.float64(100.1), 'distance_pct': np.float64(3.07), 'pin_strength': np.float64(1810200.91)}, 4850.0: {'open_interest': 51242, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.39), 'pin_strength': np.float64(251315.82)}, 4480.0: {'open_interest': 48281, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(26.46), 'pin_strength': np.float64(182448.13)}, 5310.0: {'open_interest': 45760, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(12.84), 'pin_strength': np.float64(356419.71)}}",0.7474,5,470828.75,GAMMA_PIN_CONVERGENCE,gamma
FORWARD_LOOKING,2025-07-01,6092.16,370,79886,107.74,"[5805.0, 5810.0, 5815.0, 5820.0, 5825.0, 5830.0, 5835.0, 5840.0, 5845.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6330.0, 6350.0, 6375.0, 6400.0]","[5000.0, 4900.0, 6000.0, 6050.0, 4000.0, 5670.0, 6065.0, 6300.0, 6100.0, 6060.0]","{5000.0: {'open_interest': 12915, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(72040.95)}, 4900.0: {'open_interest': 5673, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(28990.09)}, 6000.0: {'open_interest': 4726, 'gamma_exposure': np.float64(23.63), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(312408.29)}, 6050.0: {'open_interest': 3124, 'gamma_exposure': np.float64(23.74), 'distance_pct': np.float64(0.69), 'pin_strength': np.float64(451420.96)}, 4000.0: {'open_interest': 2999, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(34.34), 'pin_strength': np.float64(8732.79)}}",0.8102,6,34095.34,GAMMA_PIN_CONVERGENCE,gamma
FORWARD_LOOKING,2025-07-02,6092.16,350,62898,72.58,"[5805.0, 5810.0, 5815.0, 5820.0, 5825.0, 5830.0, 5835.0, 5840.0, 5845.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5000.0, 6100.0, 4900.0, 4200.0, 4400.0, 5500.0, 5900.0, 5750.0, 5820.0, 6175.0]","{5000.0: {'open_interest': 10404, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(58034.38)}, 6100.0: {'open_interest': 3643, 'gamma_exposure': np.float64(29.87), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(2830834.04)}, 4900.0: {'open_interest': 2485, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(12698.81)}, 4200.0: {'open_interest': 1840, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.06), 'pin_strength': np.float64(5924.22)}, 4400.0: {'open_interest': 1576, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(27.78), 'pin_strength': np.float64(5673.96)}}",0.7982,7,24417.0,GAMMA_PIN_CONVERGENCE,gamma
FORWARD_LOOKING,2025-07-03,6092.16,440,206947,210.11,"[5805.0, 5810.0, 5815.0, 5820.0, 5825.0, 5830.0, 5835.0, 5840.0, 5845.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6330.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6390.0, 6400.0]","[5950.0, 5925.0, 4800.0, 4850.0, 6100.0, 4700.0, 5000.0, 6000.0, 6200.0, 5200.0]","{5950.0: {'open_interest': 15059, 'gamma_exposure': np.float64(48.19), 'distance_pct': np.float64(2.33), 'pin_strength': np.float64(645342.13)}, 5925.0: {'open_interest': 14097, 'gamma_exposure': np.float64(36.65), 'distance_pct': np.float64(2.74), 'pin_strength': np.float64(513766.33)}, 4800.0: {'open_interest': 13146, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(21.21), 'pin_strength': np.float64(61979.58)}, 4850.0: {'open_interest': 11917, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.39), 'pin_strength': np.float64(58446.79)}, 6100.0: {'open_interest': 6073, 'gamma_exposure': np.float64(44.94), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(4719092.82)}}",0.7858,8,86296.9,GAMMA_PIN_CONVERGENCE,theta
FORWARD_LOOKING,2025-07-07,6092.16,328,65064,83.33,"[5805.0, 5810.0, 5815.0, 5820.0, 5825.0, 5830.0, 5835.0, 5840.0, 5845.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[6000.0, 5000.0, 5900.0, 6050.0, 4900.0, 5100.0, 5675.0, 5850.0, 4600.0, 6140.0]","{6000.0: {'open_interest': 9714, 'gamma_exposure': np.float64(42.74), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(642135.88)}, 5000.0: {'open_interest': 8364, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(46655.09)}, 5900.0: {'open_interest': 3843, 'gamma_exposure': np.float64(9.22), 'distance_pct': np.float64(3.15), 'pin_strength': np.float64(121836.86)}, 6050.0: {'open_interest': 3754, 'gamma_exposure': np.float64(21.02), 'distance_pct': np.float64(0.69), 'pin_strength': np.float64(542456.56)}, 4900.0: {'open_interest': 2365, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(12085.59)}}",0.8169,12,25231.82,GAMMA_PIN_CONVERGENCE,theta
FORWARD_LOOKING,2025-07-08,6092.16,316,20728,22.75,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5000.0, 6000.0, 6075.0, 6100.0, 6170.0, 6300.0, 5675.0, 4600.0, 5990.0, 5100.0]","{5000.0: {'open_interest': 4253, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(23723.59)}, 6000.0: {'open_interest': 1220, 'gamma_exposure': np.float64(4.88), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(80647.08)}, 6075.0: {'open_interest': 687, 'gamma_exposure': np.float64(3.71), 'distance_pct': np.float64(0.28), 'pin_strength': np.float64(243899.41)}, 6100.0: {'open_interest': 544, 'gamma_exposure': np.float64(3.16), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(422721.31)}, 6170.0: {'open_interest': 508, 'gamma_exposure': np.float64(2.74), 'distance_pct': np.float64(1.28), 'pin_strength': np.float64(39758.7)}}",0.8216,13,7337.71,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-09,6092.16,302,26096,22.67,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[4900.0, 5000.0, 6000.0, 6225.0, 4200.0, 6100.0, 6125.0, 4400.0, 6400.0, 5840.0]","{4900.0: {'open_interest': 4427, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(22622.8)}, 5000.0: {'open_interest': 3554, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(19824.51)}, 6000.0: {'open_interest': 1520, 'gamma_exposure': np.float64(6.08), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(100478.33)}, 6225.0: {'open_interest': 896, 'gamma_exposure': np.float64(3.58), 'distance_pct': np.float64(2.18), 'pin_strength': np.float64(41091.35)}, 4200.0: {'open_interest': 889, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.06), 'pin_strength': np.float64(2862.3)}}",0.8008,14,8601.24,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-10,6092.16,304,18598,15.2,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[4400.0, 4600.0, 5000.0, 4000.0, 6300.0, 6400.0, 5400.0, 6000.0, 6100.0, 5900.0]","{4400.0: {'open_interest': 1837, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(27.78), 'pin_strength': np.float64(6613.62)}, 4600.0: {'open_interest': 1276, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(24.49), 'pin_strength': np.float64(5209.63)}, 5000.0: {'open_interest': 1182, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(6593.3)}, 4000.0: {'open_interest': 956, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(34.34), 'pin_strength': np.float64(2783.78)}, 6300.0: {'open_interest': 677, 'gamma_exposure': np.float64(1.49), 'distance_pct': np.float64(3.41), 'pin_strength': np.float64(19844.07)}}",0.7741,15,6059.23,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-11,6092.16,378,257048,88.73,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6330.0, 6350.0, 6375.0, 6400.0]","[4700.0, 4750.0, 5000.0, 4950.0, 4900.0, 4200.0, 5900.0, 6000.0, 5600.0, 5200.0]","{4700.0: {'open_interest': 68924, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(22.85), 'pin_strength': np.float64(301614.78)}, 4750.0: {'open_interest': 64126, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(22.03), 'pin_strength': np.float64(291072.49)}, 5000.0: {'open_interest': 13528, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(75460.32)}, 4950.0: {'open_interest': 6893, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(18.75), 'pin_strength': np.float64(36766.53)}, 4900.0: {'open_interest': 5188, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(26511.65)}}",0.7535,16,86419.54,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-14,6092.16,282,31723,24.65,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5000.0, 6000.0, 5600.0, 5675.0, 5800.0, 6125.0, 5550.0, 6225.0, 4400.0, 6050.0]","{5000.0: {'open_interest': 8382, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(46755.5)}, 6000.0: {'open_interest': 2238, 'gamma_exposure': np.float64(7.61), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(147941.13)}, 5600.0: {'open_interest': 1794, 'gamma_exposure': np.float64(1.08), 'distance_pct': np.float64(8.08), 'pin_strength': np.float64(22206.87)}, 5675.0: {'open_interest': 1426, 'gamma_exposure': np.float64(1.14), 'distance_pct': np.float64(6.85), 'pin_strength': np.float64(20825.15)}, 5800.0: {'open_interest': 1258, 'gamma_exposure': np.float64(1.76), 'distance_pct': np.float64(4.8), 'pin_strength': np.float64(26231.99)}}",0.7581,19,9275.81,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-15,6092.16,270,19967,9.34,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5000.0, 4200.0, 2400.0, 6000.0, 6100.0, 6300.0, 5700.0, 5950.0, 5625.0, 6050.0]","{5000.0: {'open_interest': 10048, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(56048.59)}, 4200.0: {'open_interest': 681, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.06), 'pin_strength': np.float64(2192.61)}, 2400.0: {'open_interest': 512, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(60.61), 'pin_strength': np.float64(844.81)}, 6000.0: {'open_interest': 467, 'gamma_exposure': np.float64(1.59), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(30870.65)}, 6100.0: {'open_interest': 436, 'gamma_exposure': np.float64(2.01), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(338798.69)}}",0.7924,20,5474.95,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-16,6092.16,236,26482,16.17,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5000.0, 2400.0, 6100.0, 6300.0, 6050.0, 5450.0, 5975.0, 6090.0, 5300.0, 5225.0]","{5000.0: {'open_interest': 10010, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(55836.62)}, 2400.0: {'open_interest': 2060, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(60.61), 'pin_strength': np.float64(3399.05)}, 6100.0: {'open_interest': 1117, 'gamma_exposure': np.float64(4.69), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(867977.39)}, 6300.0: {'open_interest': 1017, 'gamma_exposure': np.float64(2.64), 'distance_pct': np.float64(3.41), 'pin_strength': np.float64(29810.08)}, 6050.0: {'open_interest': 734, 'gamma_exposure': np.float64(2.94), 'distance_pct': np.float64(0.69), 'pin_strength': np.float64(106063.7)}}",0.8139,21,5312.29,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-17,6092.16,236,14889,6.11,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5000.0, 5575.0, 4200.0, 2400.0, 5950.0, 2800.0, 5675.0, 5475.0, 6300.0, 4600.0]","{5000.0: {'open_interest': 5067, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(28264.15)}, 5575.0: {'open_interest': 910, 'gamma_exposure': np.float64(0.36), 'distance_pct': np.float64(8.49), 'pin_strength': np.float64(10719.83)}, 4200.0: {'open_interest': 691, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.06), 'pin_strength': np.float64(2224.8)}, 2400.0: {'open_interest': 573, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(60.61), 'pin_strength': np.float64(945.46)}, 5950.0: {'open_interest': 425, 'gamma_exposure': np.float64(1.1), 'distance_pct': np.float64(2.33), 'pin_strength': np.float64(18213.06)}}",0.7178,22,2852.73,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-18,6092.16,1788,2131579,1053.44,"[5805.0, 5810.0, 5815.0, 5820.0, 5825.0, 5830.0, 5835.0, 5840.0, 5845.0, 5850.0, 5855.0, 5860.0, 5865.0, 5870.0, 5875.0, 5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6330.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6390.0, 6400.0, 6410.0]","[5000.0, 6000.0, 4800.0, 4750.0, 5500.0, 6050.0, 5800.0, 5600.0, 5900.0, 5400.0]","{5000.0: {'open_interest': 173712, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(968980.09)}, 6000.0: {'open_interest': 173361, 'gamma_exposure': np.float64(1074.84), 'distance_pct': np.float64(1.51), 'pin_strength': np.float64(11459884.44)}, 4800.0: {'open_interest': 88153, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(21.21), 'pin_strength': np.float64(415615.85)}, 4750.0: {'open_interest': 72106, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(22.03), 'pin_strength': np.float64(327294.28)}, 5500.0: {'open_interest': 45569, 'gamma_exposure': np.float64(18.23), 'distance_pct': np.float64(9.72), 'pin_strength': np.float64(468815.25)}}",0.7483,23,1572252.67,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-21,6092.16,226,15139,18.09,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5975.0, 6100.0, 6150.0, 4000.0, 4400.0, 6225.0, 6125.0, 6200.0, 6175.0, 5225.0]","{5975.0: {'open_interest': 1359, 'gamma_exposure': np.float64(3.81), 'distance_pct': np.float64(1.92), 'pin_strength': np.float64(70666.14)}, 6100.0: {'open_interest': 1249, 'gamma_exposure': np.float64(4.75), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(970549.47)}, 6150.0: {'open_interest': 1038, 'gamma_exposure': np.float64(4.15), 'distance_pct': np.float64(0.95), 'pin_strength': np.float64(109330.26)}, 4000.0: {'open_interest': 745, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(34.34), 'pin_strength': np.float64(2169.37)}, 4400.0: {'open_interest': 584, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(27.78), 'pin_strength': np.float64(2102.53)}}",0.8496,26,2834.02,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-22,6092.16,216,3228,2.89,"[5825.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[4000.0, 5700.0, 6150.0, 6100.0, 6125.0, 6000.0, 6275.0, 5675.0, 6250.0, 5750.0]","{4000.0: {'open_interest': 631, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(34.34), 'pin_strength': np.float64(1837.41)}, 5700.0: {'open_interest': 299, 'gamma_exposure': np.float64(0.3), 'distance_pct': np.float64(6.44), 'pin_strength': np.float64(4644.93)}, 6150.0: {'open_interest': 271, 'gamma_exposure': np.float64(1.03), 'distance_pct': np.float64(0.95), 'pin_strength': np.float64(28543.83)}, 6100.0: {'open_interest': 234, 'gamma_exposure': np.float64(0.89), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(181832.33)}, 6125.0: {'open_interest': 140, 'gamma_exposure': np.float64(0.53), 'distance_pct': np.float64(0.54), 'pin_strength': np.float64(25971.45)}}",0.7913,27,571.36,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-23,6092.16,206,3587,2.62,"[5825.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6175.0, 6200.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[4000.0, 4900.0, 6225.0, 6200.0, 6350.0, 6010.0, 6300.0, 5000.0, 5450.0, 6100.0]","{4000.0: {'open_interest': 621, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(34.34), 'pin_strength': np.float64(1808.29)}, 4900.0: {'open_interest': 282, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(1441.07)}, 6225.0: {'open_interest': 198, 'gamma_exposure': np.float64(0.67), 'distance_pct': np.float64(2.18), 'pin_strength': np.float64(9080.46)}, 6200.0: {'open_interest': 163, 'gamma_exposure': np.float64(0.55), 'distance_pct': np.float64(1.77), 'pin_strength': np.float64(9208.29)}, 6350.0: {'open_interest': 129, 'gamma_exposure': np.float64(0.26), 'distance_pct': np.float64(4.23), 'pin_strength': np.float64(3047.97)}}",0.7885,28,537.33,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-24,6092.16,206,4673,5.35,"[5825.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6175.0, 6200.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[6050.0, 6075.0, 6300.0, 5775.0, 5800.0, 5975.0, 6325.0, 6150.0, 6100.0, 4800.0]","{6050.0: {'open_interest': 1556, 'gamma_exposure': np.float64(4.98), 'distance_pct': np.float64(0.69), 'pin_strength': np.float64(224843.48)}, 6075.0: {'open_interest': 178, 'gamma_exposure': np.float64(0.61), 'distance_pct': np.float64(0.28), 'pin_strength': np.float64(63193.73)}, 6300.0: {'open_interest': 176, 'gamma_exposure': np.float64(0.46), 'distance_pct': np.float64(3.41), 'pin_strength': np.float64(5158.87)}, 5775.0: {'open_interest': 142, 'gamma_exposure': np.float64(0.2), 'distance_pct': np.float64(5.21), 'pin_strength': np.float64(2727.6)}, 5800.0: {'open_interest': 135, 'gamma_exposure': np.float64(0.19), 'distance_pct': np.float64(4.8), 'pin_strength': np.float64(2815.04)}}",0.8309,29,703.75,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-25,6092.16,274,168465,26.4,"[5810.0, 5820.0, 5825.0, 5830.0, 5840.0, 5850.0, 5860.0, 5870.0, 5875.0, 5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[4950.0, 4900.0, 4700.0, 6700.0, 4850.0, 5100.0, 6450.0, 5750.0, 5600.0, 6250.0]","{4950.0: {'open_interest': 61406, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(18.75), 'pin_strength': np.float64(327533.08)}, 4900.0: {'open_interest': 60307, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.57), 'pin_strength': np.float64(308180.02)}, 4700.0: {'open_interest': 6170, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(22.85), 'pin_strength': np.float64(27000.22)}, 6700.0: {'open_interest': 2422, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(9.98), 'pin_strength': np.float64(24274.83)}, 4850.0: {'open_interest': 1610, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.39), 'pin_strength': np.float64(7896.23)}}",0.7099,30,32345.28,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-28,6092.16,194,5717,3.98,"[5825.0, 5850.0, 5875.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6175.0, 6200.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0]","[5400.0, 5800.0, 5000.0, 5950.0, 6100.0, 6000.0, 5900.0, 6150.0, 6125.0, 5975.0]","{5400.0: {'open_interest': 1452, 'gamma_exposure': np.float64(0.58), 'distance_pct': np.float64(11.36), 'pin_strength': np.float64(12780.02)}, 5800.0: {'open_interest': 708, 'gamma_exposure': np.float64(0.99), 'distance_pct': np.float64(4.8), 'pin_strength': np.float64(14763.31)}, 5000.0: {'open_interest': 702, 'gamma_exposure': np.float64(0.14), 'distance_pct': np.float64(17.93), 'pin_strength': np.float64(3915.81)}, 5950.0: {'open_interest': 471, 'gamma_exposure': np.float64(1.13), 'distance_pct': np.float64(2.33), 'pin_strength': np.float64(20184.35)}, 6100.0: {'open_interest': 261, 'gamma_exposure': np.float64(0.89), 'distance_pct': np.float64(0.13), 'pin_strength': np.float64(202812.98)}}",0.7844,33,807.24,VEGA_SENSITIVITY_SHIFT,vega
