analysis_type,date,current_price,total_contracts,total_open_interest,total_gamma_exposure,atm_strikes,high_oi_strikes,pin_risk_strikes,convergence_pressure,days_to_target,bearish_signal,bullish_signal,explosive_signal,signal_strength,gamma_exposure,expected_analysis_type,expected_primary_greek
FORWARD_LOOKING,2025-07-01,6173.07,390,119338,185.14,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6285.0, 6290.0, 6295.0, 6300.0, 6305.0, 6310.0, 6315.0, 6320.0, 6325.0, 6330.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 6000.0, 6150.0, 4900.0, 6050.0, 6275.0, 4000.0, 5670.0, 6065.0, 5800.0]","{5000.0: {'open_interest': 12916, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(67968.13)}, 6000.0: {'open_interest': 11582, 'gamma_exposure': np.float64(23.16), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(413107.39)}, 6150.0: {'open_interest': 8548, 'gamma_exposure': np.float64(109.41), 'distance_pct': np.float64(0.37), 'pin_strength': np.float64(2287273.62)}, 4900.0: {'open_interest': 5685, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.62), 'pin_strength': np.float64(27566.36)}, 6050.0: {'open_interest': 4481, 'gamma_exposure': np.float64(18.82), 'distance_pct': np.float64(1.99), 'pin_strength': np.float64(224762.55)}}",0.8042,4,0,0,0,6.195480219047619,51028.93,GAMMA_PIN_CONVERGENCE,gamma
FORWARD_LOOKING,2025-07-02,6173.07,390,98816,135.49,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6285.0, 6290.0, 6295.0, 6300.0, 6305.0, 6310.0, 6315.0, 6320.0, 6325.0, 6330.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 6100.0, 5750.0, 4900.0, 5500.0, 6150.0, 4600.0, 4200.0, 6250.0, 5900.0]","{5000.0: {'open_interest': 10405, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(54754.44)}, 6100.0: {'open_interest': 4364, 'gamma_exposure': np.float64(32.29), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(368677.67)}, 5750.0: {'open_interest': 3536, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(6.85), 'pin_strength': np.float64(51594.24)}, 4900.0: {'open_interest': 2934, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.62), 'pin_strength': np.float64(14226.86)}, 5500.0: {'open_interest': 2336, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(10.9), 'pin_strength': np.float64(21424.65)}}",0.7921,5,0,0,0,5.890768423809524,41384.14,GAMMA_PIN_CONVERGENCE,gamma
FORWARD_LOOKING,2025-07-03,6173.07,472,245852,249.42,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6285.0, 6290.0, 6295.0, 6300.0, 6305.0, 6310.0, 6315.0, 6320.0, 6325.0, 6330.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6390.0, 6400.0, 6410.0, 6420.0, 6425.0, 6430.0, 6440.0, 6450.0, 6475.0]","[5950.0, 5925.0, 4800.0, 4850.0, 6100.0, 6200.0, 5000.0, 6000.0, 4700.0, 5200.0]","{5950.0: {'open_interest': 15126, 'gamma_exposure': np.float64(21.18), 'distance_pct': np.float64(3.61), 'pin_strength': np.float64(418585.45)}, 5925.0: {'open_interest': 14958, 'gamma_exposure': np.float64(17.95), 'distance_pct': np.float64(4.02), 'pin_strength': np.float64(372220.67)}, 4800.0: {'open_interest': 13156, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(22.24), 'pin_strength': np.float64(59146.95)}, 4850.0: {'open_interest': 11913, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(21.43), 'pin_strength': np.float64(55582.68)}, 6100.0: {'open_interest': 8467, 'gamma_exposure': np.float64(57.58), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(715305.65)}}",0.7774,6,0,0,0,6.063830828571429,102520.28,GAMMA_PIN_CONVERGENCE,gamma
FORWARD_LOOKING,2025-07-07,6173.07,348,77090,95.66,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[6000.0, 5000.0, 6050.0, 5900.0, 6100.0, 4900.0, 5100.0, 6075.0, 5675.0, 4600.0]","{6000.0: {'open_interest': 9764, 'gamma_exposure': np.float64(27.34), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(348262.87)}, 5000.0: {'open_interest': 8367, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(44029.83)}, 6050.0: {'open_interest': 4214, 'gamma_exposure': np.float64(17.7), 'distance_pct': np.float64(1.99), 'pin_strength': np.float64(211370.09)}, 5900.0: {'open_interest': 3997, 'gamma_exposure': np.float64(5.6), 'distance_pct': np.float64(4.42), 'pin_strength': np.float64(90356.91)}, 6100.0: {'open_interest': 2795, 'gamma_exposure': np.float64(16.77), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(236126.05)}}",0.783,10,0,0,0,5.249599647619047,31159.78,GAMMA_PIN_CONVERGENCE,theta
FORWARD_LOOKING,2025-07-08,6173.07,336,24811,29.9,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 6000.0, 6300.0, 6170.0, 6075.0, 6100.0, 6010.0, 6070.0, 5675.0, 4600.0]","{5000.0: {'open_interest': 4289, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(22570.09)}, 6000.0: {'open_interest': 1309, 'gamma_exposure': np.float64(3.93), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(46689.48)}, 6300.0: {'open_interest': 991, 'gamma_exposure': np.float64(2.97), 'distance_pct': np.float64(2.06), 'pin_strength': np.float64(48195.95)}, 6170.0: {'open_interest': 760, 'gamma_exposure': np.float64(5.32), 'distance_pct': np.float64(0.05), 'pin_strength': np.float64(1528186.71)}, 6075.0: {'open_interest': 696, 'gamma_exposure': np.float64(3.48), 'distance_pct': np.float64(1.59), 'pin_strength': np.float64(43810.1)}}",0.7991,11,0,0,0,5.037571052380953,9651.48,GAMMA_PIN_CONVERGENCE,theta
FORWARD_LOOKING,2025-07-09,6173.07,336,34075,33.95,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[4900.0, 5000.0, 6000.0, 6100.0, 5100.0, 6225.0, 4200.0, 5850.0, 6125.0, 4400.0]","{4900.0: {'open_interest': 4552, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.62), 'pin_strength': np.float64(22072.48)}, 5000.0: {'open_interest': 3554, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(18702.29)}, 6000.0: {'open_interest': 1601, 'gamma_exposure': np.float64(4.8), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(57104.55)}, 6100.0: {'open_interest': 1091, 'gamma_exposure': np.float64(5.89), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(92169.42)}, 5100.0: {'open_interest': 1002, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(17.38), 'pin_strength': np.float64(5764.22)}}",0.7971,12,0,0,0,4.944499457142856,13071.17,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-10,6173.07,338,27293,28.03,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[4400.0, 6140.0, 5000.0, 4600.0, 4000.0, 5400.0, 6100.0, 6000.0, 5700.0, 6300.0]","{4400.0: {'open_interest': 1784, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(28.72), 'pin_strength': np.float64(6211.12)}, 6140.0: {'open_interest': 1462, 'gamma_exposure': np.float64(8.48), 'distance_pct': np.float64(0.54), 'pin_strength': np.float64(272906.81)}, 5000.0: {'open_interest': 1266, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(6662.1)}, 4600.0: {'open_interest': 1260, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(25.48), 'pin_strength': np.float64(4944.51)}, 4000.0: {'open_interest': 950, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(35.2), 'pin_strength': np.float64(2698.68)}}",0.7822,13,0,0,0,4.756994161904762,10278.54,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-11,6173.07,400,285717,123.09,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6330.0, 6340.0, 6350.0, 6360.0, 6370.0, 6375.0, 6380.0, 6400.0, 6425.0, 6450.0, 6475.0]","[4700.0, 4750.0, 5000.0, 4200.0, 4950.0, 6150.0, 4900.0, 6000.0, 5900.0, 5950.0]","{4700.0: {'open_interest': 71972, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(23.86), 'pin_strength': np.float64(301606.98)}, 4750.0: {'open_interest': 64734, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(23.05), 'pin_strength': np.float64(280806.65)}, 5000.0: {'open_interest': 13724, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(72220.08)}, 4200.0: {'open_interest': 7319, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.96), 'pin_strength': np.float64(22898.68)}, 4950.0: {'open_interest': 6900, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.81), 'pin_strength': np.float64(34825.63)}}",0.7742,14,0,0,0,4.906948966666667,100686.67,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-14,6173.07,302,35446,28.57,"[5880.0, 5890.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 6000.0, 5600.0, 5675.0, 5800.0, 5100.0, 6125.0, 6225.0, 5550.0, 6100.0]","{5000.0: {'open_interest': 8471, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(44577.11)}, 6000.0: {'open_interest': 2268, 'gamma_exposure': np.float64(6.35), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(80895.15)}, 5600.0: {'open_interest': 1794, 'gamma_exposure': np.float64(0.72), 'distance_pct': np.float64(9.28), 'pin_strength': np.float64(19324.84)}, 5675.0: {'open_interest': 1424, 'gamma_exposure': np.float64(0.85), 'distance_pct': np.float64(8.07), 'pin_strength': np.float64(17649.03)}, 5800.0: {'open_interest': 1271, 'gamma_exposure': np.float64(1.27), 'distance_pct': np.float64(6.04), 'pin_strength': np.float64(21030.83)}}",0.7523,17,0,0,0,4.228153680952381,11371.08,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-15,6173.07,290,23553,11.42,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 4200.0, 6250.0, 2400.0, 6000.0, 6300.0, 6100.0, 5700.0, 5950.0, 5625.0]","{5000.0: {'open_interest': 11709, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(61616.51)}, 4200.0: {'open_interest': 681, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.96), 'pin_strength': np.float64(2130.62)}, 6250.0: {'open_interest': 604, 'gamma_exposure': np.float64(2.78), 'distance_pct': np.float64(1.25), 'pin_strength': np.float64(48466.58)}, 2400.0: {'open_interest': 512, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(61.12), 'pin_strength': np.float64(837.68)}, 6000.0: {'open_interest': 497, 'gamma_exposure': np.float64(1.39), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(17727.02)}}",0.774,18,0,1,0,4.189983285714286,6976.4,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-16,6173.07,290,28863,18.32,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 2400.0, 6300.0, 6100.0, 5450.0, 6050.0, 5300.0, 5975.0, 6090.0, 6200.0]","{5000.0: {'open_interest': 10098, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(53138.91)}, 2400.0: {'open_interest': 2060, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(61.12), 'pin_strength': np.float64(3370.34)}, 6300.0: {'open_interest': 1327, 'gamma_exposure': np.float64(4.78), 'distance_pct': np.float64(2.06), 'pin_strength': np.float64(64536.86)}, 6100.0: {'open_interest': 1031, 'gamma_exposure': np.float64(4.33), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(87100.52)}, 5450.0: {'open_interest': 743, 'gamma_exposure': np.float64(0.15), 'distance_pct': np.float64(11.71), 'pin_strength': np.float64(6343.22)}}",0.7873,19,0,1,0,4.181925390476191,8468.4,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-17,6173.07,282,16686,8.21,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5000.0, 5575.0, 4200.0, 2400.0, 5950.0, 2800.0, 5675.0, 5475.0, 6300.0, 4600.0]","{5000.0: {'open_interest': 5163, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(27169.36)}, 5575.0: {'open_interest': 911, 'gamma_exposure': np.float64(0.36), 'distance_pct': np.float64(9.69), 'pin_strength': np.float64(9403.02)}, 4200.0: {'open_interest': 691, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.96), 'pin_strength': np.float64(2161.91)}, 2400.0: {'open_interest': 573, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(61.12), 'pin_strength': np.float64(937.48)}, 5950.0: {'open_interest': 441, 'gamma_exposure': np.float64(0.97), 'distance_pct': np.float64(3.61), 'pin_strength': np.float64(12203.9)}}",0.7193,20,0,1,0,3.7163572952380957,4642.05,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-18,6173.07,1956,2141776,1036.16,"[5880.0, 5885.0, 5890.0, 5895.0, 5900.0, 5905.0, 5910.0, 5915.0, 5920.0, 5925.0, 5930.0, 5935.0, 5940.0, 5945.0, 5950.0, 5955.0, 5960.0, 5965.0, 5970.0, 5975.0, 5980.0, 5985.0, 5990.0, 5995.0, 6000.0, 6005.0, 6010.0, 6015.0, 6020.0, 6025.0, 6030.0, 6035.0, 6040.0, 6045.0, 6050.0, 6055.0, 6060.0, 6065.0, 6070.0, 6075.0, 6080.0, 6085.0, 6090.0, 6095.0, 6100.0, 6105.0, 6110.0, 6115.0, 6120.0, 6125.0, 6130.0, 6135.0, 6140.0, 6145.0, 6150.0, 6155.0, 6160.0, 6165.0, 6170.0, 6175.0, 6180.0, 6185.0, 6190.0, 6195.0, 6200.0, 6205.0, 6210.0, 6215.0, 6220.0, 6225.0, 6230.0, 6235.0, 6240.0, 6245.0, 6250.0, 6255.0, 6260.0, 6265.0, 6270.0, 6275.0, 6280.0, 6285.0, 6290.0, 6295.0, 6300.0, 6305.0, 6310.0, 6315.0, 6320.0, 6325.0, 6330.0, 6335.0, 6340.0, 6345.0, 6350.0, 6355.0, 6360.0, 6365.0, 6370.0, 6375.0, 6380.0, 6385.0, 6390.0, 6395.0, 6400.0, 6405.0, 6410.0, 6415.0, 6420.0, 6425.0, 6430.0, 6435.0, 6440.0, 6450.0, 6460.0, 6470.0, 6475.0, 6480.0, 6490.0]","[5000.0, 6000.0, 4800.0, 4750.0, 5500.0, 6050.0, 5900.0, 5600.0, 5800.0, 2500.0]","{5000.0: {'open_interest': 167421, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(881022.92)}, 6000.0: {'open_interest': 165730, 'gamma_exposure': np.float64(861.8), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(5911266.49)}, 4800.0: {'open_interest': 88155, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(22.24), 'pin_strength': np.float64(396328.65)}, 4750.0: {'open_interest': 72037, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(23.05), 'pin_strength': np.float64(312485.99)}, 5500.0: {'open_interest': 45593, 'gamma_exposure': np.float64(18.24), 'distance_pct': np.float64(10.9), 'pin_strength': np.float64(418156.77)}}",0.7392,21,0,0,0,6.696,1818796.18,THETA_DECAY_ACCELERATION,theta
FORWARD_LOOKING,2025-07-21,6173.07,238,16696,18.91,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5975.0, 6100.0, 6150.0, 4000.0, 6175.0, 6200.0, 6225.0, 4400.0, 6125.0, 4800.0]","{5975.0: {'open_interest': 1360, 'gamma_exposure': np.float64(2.99), 'distance_pct': np.float64(3.21), 'pin_strength': np.float64(42385.9)}, 6100.0: {'open_interest': 1148, 'gamma_exposure': np.float64(4.13), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(96984.87)}, 6150.0: {'open_interest': 1122, 'gamma_exposure': np.float64(4.49), 'distance_pct': np.float64(0.37), 'pin_strength': np.float64(300224.73)}, 4000.0: {'open_interest': 745, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(35.2), 'pin_strength': np.float64(2116.33)}, 6175.0: {'open_interest': 628, 'gamma_exposure': np.float64(2.51), 'distance_pct': np.float64(0.03), 'pin_strength': np.float64(2008646.61)}}",0.8347,24,0,1,0,4.230231799999999,3188.94,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-22,6173.07,228,4564,4.25,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[4000.0, 6000.0, 6150.0, 5700.0, 6100.0, 5990.0, 5800.0, 6125.0, 6275.0, 4900.0]","{4000.0: {'open_interest': 631, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(35.2), 'pin_strength': np.float64(1792.49)}, 6000.0: {'open_interest': 364, 'gamma_exposure': np.float64(0.95), 'distance_pct': np.float64(2.8), 'pin_strength': np.float64(12983.17)}, 6150.0: {'open_interest': 355, 'gamma_exposure': np.float64(1.42), 'distance_pct': np.float64(0.37), 'pin_strength': np.float64(94990.89)}, 5700.0: {'open_interest': 299, 'gamma_exposure': np.float64(0.24), 'distance_pct': np.float64(7.66), 'pin_strength': np.float64(3901.64)}, 6100.0: {'open_interest': 244, 'gamma_exposure': np.float64(0.88), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(20613.51)}}",0.7796,25,0,1,0,3.9107619999999996,856.21,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-23,6173.07,228,7587,7.05,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[6275.0, 5875.0, 6200.0, 4000.0, 5500.0, 5850.0, 4900.0, 6350.0, 6225.0, 6250.0]","{6275.0: {'open_interest': 818, 'gamma_exposure': np.float64(2.94), 'distance_pct': np.float64(1.65), 'pin_strength': np.float64(49539.6)}, 5875.0: {'open_interest': 813, 'gamma_exposure': np.float64(1.3), 'distance_pct': np.float64(4.83), 'pin_strength': np.float64(16837.34)}, 6200.0: {'open_interest': 667, 'gamma_exposure': np.float64(2.67), 'distance_pct': np.float64(0.44), 'pin_strength': np.float64(152894.08)}, 4000.0: {'open_interest': 621, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(35.2), 'pin_strength': np.float64(1764.08)}, 5500.0: {'open_interest': 529, 'gamma_exposure': np.float64(0.11), 'distance_pct': np.float64(10.9), 'pin_strength': np.float64(4851.73)}}",0.7972,26,0,1,0,4.0071401,1347.45,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-24,6173.07,228,6743,6.23,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[6050.0, 4200.0, 5650.0, 6300.0, 6325.0, 5750.0, 6075.0, 5800.0, 5775.0, 5975.0]","{6050.0: {'open_interest': 1574, 'gamma_exposure': np.float64(4.41), 'distance_pct': np.float64(1.99), 'pin_strength': np.float64(78950.29)}, 4200.0: {'open_interest': 652, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(31.96), 'pin_strength': np.float64(2039.89)}, 5650.0: {'open_interest': 222, 'gamma_exposure': np.float64(0.18), 'distance_pct': np.float64(8.47), 'pin_strength': np.float64(2619.96)}, 6300.0: {'open_interest': 218, 'gamma_exposure': np.float64(0.74), 'distance_pct': np.float64(2.06), 'pin_strength': np.float64(10602.14)}, 6325.0: {'open_interest': 216, 'gamma_exposure': np.float64(0.65), 'distance_pct': np.float64(2.46), 'pin_strength': np.float64(8776.3)}}",0.7787,27,0,1,0,3.9121911999999996,1190.81,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-25,6173.07,302,179776,35.29,"[5880.0, 5890.0, 5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6210.0, 6220.0, 6225.0, 6230.0, 6240.0, 6250.0, 6260.0, 6270.0, 6275.0, 6280.0, 6290.0, 6300.0, 6310.0, 6320.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[4950.0, 4900.0, 4700.0, 6700.0, 5750.0, 5600.0, 6450.0, 4850.0, 5100.0, 6250.0]","{4950.0: {'open_interest': 61413, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.81), 'pin_strength': np.float64(309963.25)}, 4900.0: {'open_interest': 60370, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(20.62), 'pin_strength': np.float64(292731.93)}, 4700.0: {'open_interest': 6193, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(23.86), 'pin_strength': np.float64(25952.48)}, 6700.0: {'open_interest': 2423, 'gamma_exposure': np.float64(0.48), 'distance_pct': np.float64(8.54), 'pin_strength': np.float64(28385.84)}, 5750.0: {'open_interest': 1788, 'gamma_exposure': np.float64(1.79), 'distance_pct': np.float64(6.85), 'pin_strength': np.float64(26088.94)}}",0.7162,28,0,1,0,3.6868729999999994,38543.97,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-28,6173.07,206,7000,4.84,"[5900.0, 5910.0, 5920.0, 5925.0, 5930.0, 5940.0, 5950.0, 5960.0, 5970.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5400.0, 5800.0, 5000.0, 5950.0, 6100.0, 6000.0, 5900.0, 6150.0, 6125.0, 5500.0]","{5400.0: {'open_interest': 1454, 'gamma_exposure': np.float64(0.58), 'distance_pct': np.float64(12.52), 'pin_strength': np.float64(11610.39)}, 5800.0: {'open_interest': 784, 'gamma_exposure': np.float64(0.94), 'distance_pct': np.float64(6.04), 'pin_strength': np.float64(12972.6)}, 5000.0: {'open_interest': 740, 'gamma_exposure': np.float64(0.0), 'distance_pct': np.float64(19.0), 'pin_strength': np.float64(3894.12)}, 5950.0: {'open_interest': 471, 'gamma_exposure': np.float64(0.94), 'distance_pct': np.float64(3.61), 'pin_strength': np.float64(13034.1)}, 6100.0: {'open_interest': 315, 'gamma_exposure': np.float64(1.01), 'distance_pct': np.float64(1.18), 'pin_strength': np.float64(26611.7)}}",0.7663,31,0,1,0,3.84602,1069.6,VEGA_SENSITIVITY_SHIFT,vega
FORWARD_LOOKING,2025-07-29,6173.07,192,2395,2.0,"[5900.0, 5925.0, 5950.0, 5975.0, 5980.0, 5990.0, 6000.0, 6010.0, 6020.0, 6025.0, 6030.0, 6040.0, 6050.0, 6060.0, 6070.0, 6075.0, 6080.0, 6090.0, 6100.0, 6110.0, 6120.0, 6125.0, 6130.0, 6140.0, 6150.0, 6160.0, 6170.0, 6175.0, 6180.0, 6190.0, 6200.0, 6225.0, 6250.0, 6275.0, 6300.0, 6325.0, 6350.0, 6375.0, 6400.0, 6425.0, 6450.0, 6475.0]","[5450.0, 5775.0, 5900.0, 6150.0, 6125.0, 6025.0, 6200.0, 5750.0, 5875.0, 6030.0]","{5450.0: {'open_interest': 204, 'gamma_exposure': np.float64(0.08), 'distance_pct': np.float64(11.71), 'pin_strength': np.float64(1741.61)}, 5775.0: {'open_interest': 179, 'gamma_exposure': np.float64(0.21), 'distance_pct': np.float64(6.45), 'pin_strength': np.float64(2775.84)}, 5900.0: {'open_interest': 99, 'gamma_exposure': np.float64(0.16), 'distance_pct': np.float64(4.42), 'pin_strength': np.float64(2238.01)}, 6150.0: {'open_interest': 93, 'gamma_exposure': np.float64(0.3), 'distance_pct': np.float64(0.37), 'pin_strength': np.float64(24884.94)}, 6125.0: {'open_interest': 84, 'gamma_exposure': np.float64(0.27), 'distance_pct': np.float64(0.78), 'pin_strength': np.float64(10787.14)}}",0.7619,32,0,1,0,3.8154922,328.12,VEGA_SENSITIVITY_SHIFT,vega
