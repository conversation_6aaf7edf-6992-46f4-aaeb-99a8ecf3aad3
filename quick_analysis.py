#!/usr/bin/env python3
"""
Quick analysis script with data sampling for faster processing
"""
import sys
import os
sys.path.append('./src')
sys.path.append('./config')

import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Import our modules
from data_loader import SPXDataLoader
from analytics_engine import GreeksCalculator, PortfolioGreeksCalculator
from config import *

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def quick_convergence_analysis():
    """Run a quick convergence analysis with data sampling"""
    print("=== Quick Convergence Analysis ===")
    
    # Load data
    data_loader = SPXDataLoader(SPX_DATA_FILE)
    data_loader.load_raw_data()
    data_loader.clean_data()
    
    # Initialize calculators
    greeks_calc = GreeksCalculator()
    portfolio_calc = PortfolioGreeksCalculator(greeks_calc)
    
    results = []
    
    # Analyze each convergence date
    for date in CONVERGENCE_DATES:
        print(f"\nAnalyzing {date}...")
        
        # Get options data for the date
        options_data = data_loader.get_data_by_date(date)
        
        if options_data.empty:
            print(f"  No data for {date}")
            continue
        
        print(f"  Total options: {len(options_data)}")
        
        # Sample data for faster processing - focus on liquid options
        # Filter for options with significant open interest and reasonable strikes
        spot_price = options_data['spx_close'].iloc[0]
        
        # Focus on options within 20% of spot price and with OI > 10
        filtered_data = options_data[
            (options_data['moneyness'] >= 0.8) & 
            (options_data['moneyness'] <= 1.2) &
            (options_data['Open Interest'] > 10) &
            (options_data['tte'] > 0.01) &  # At least 3-4 days
            (options_data['tte'] < 1.0)     # Less than 1 year
        ].copy()
        
        print(f"  Filtered options: {len(filtered_data)}")
        
        if filtered_data.empty:
            print(f"  No suitable options after filtering")
            continue
        
        # Calculate portfolio Greeks for filtered data
        portfolio_greeks = {}
        total_delta = 0
        total_gamma = 0
        total_vega = 0
        total_theta = 0
        total_vomma = 0
        total_vanna = 0
        total_charm = 0
        total_gex = 0
        total_oi = 0
        total_volume = 0
        
        # Process in smaller batches
        batch_size = 1000
        for i in range(0, len(filtered_data), batch_size):
            batch = filtered_data.iloc[i:i+batch_size]
            
            for _, row in batch.iterrows():
                S = row['spx_close']
                K = row['Strike']
                T = row['tte']
                sigma = row['iv_mid']
                option_type = 'call' if row['is_call'] else 'put'
                oi = row['Open Interest']
                volume = row['Volume']
                
                if sigma <= 0 or T <= 0:
                    continue
                
                # Calculate Greeks
                try:
                    greeks = greeks_calc.calculate_all_greeks(S, K, T, sigma, option_type)
                    
                    # Weight by open interest
                    total_delta += greeks['delta'] * oi
                    total_gamma += greeks['gamma'] * oi
                    total_vega += greeks['vega'] * oi
                    total_theta += greeks['theta'] * oi
                    total_vomma += greeks['vomma'] * oi
                    total_vanna += greeks['vanna'] * oi
                    total_charm += greeks['charm'] * oi
                    
                    # GEX calculation
                    gex = greeks['gamma'] * oi * S**2 * 0.01
                    total_gex += gex
                    
                    total_oi += oi
                    total_volume += volume
                    
                except Exception as e:
                    continue
        
        # Calculate additional metrics
        call_volume = filtered_data[filtered_data['is_call'] == 1]['Volume'].sum()
        put_volume = filtered_data[filtered_data['is_call'] == 0]['Volume'].sum()
        volm_bs = call_volume - put_volume
        
        # VxOI calculation
        if total_oi > 0:
            weighted_iv = (filtered_data['iv_mid'] * filtered_data['Open Interest']).sum() / total_oi
            vx_oi = weighted_iv * total_oi / 1000
        else:
            vx_oi = 0
        
        # Store results
        result = {
            'date': date,
            'spot_price': spot_price,
            'total_delta': total_delta,
            'total_gamma': total_gamma,
            'total_vega': total_vega,
            'total_theta': total_theta,
            'total_vomma': total_vomma,
            'total_vanna': total_vanna,
            'total_charm': total_charm,
            'gex': total_gex,
            'total_open_interest': total_oi,
            'total_volume': total_volume,
            'volm_bs': volm_bs,
            'vx_oi': vx_oi,
            # Scaled values
            'charm_scaled': total_charm / 1000,
            'vanna_scaled': total_vanna / 1000,
            'gex_scaled': total_gex / 1000000,
            'vomma_scaled': total_vomma / 1000000,
            'volm_bs_scaled': volm_bs / 1000,
            'vx_oi_scaled': vx_oi / 1000000
        }
        
        # Calculate signals
        bearish_signal = 1 if (result['charm_scaled'] > 100 and result['gex_scaled'] > 100) else 0
        bullish_signal = 1 if (result['vomma_scaled'] < -50 and result['vanna_scaled'] > 50 and result['gex_scaled'] < 50) else 0
        explosive_signal = 1 if (result['vomma_scaled'] < -100 and result['vanna_scaled'] > 200 and 
                               result['gex_scaled'] > 10 and result['gex_scaled'] < 200 and result['charm_scaled'] < 0) else 0
        
        result['bearish_signal'] = bearish_signal
        result['bullish_signal'] = bullish_signal
        result['explosive_signal'] = explosive_signal
        result['signal_strength'] = explosive_signal * 3 + bullish_signal * 2 + bearish_signal * (-1)
        
        results.append(result)
        
        print(f"  Results:")
        print(f"    Spot: {spot_price:.2f}")
        print(f"    Charm: {result['charm_scaled']:.1f}K")
        print(f"    Vanna: {result['vanna_scaled']:.1f}K")
        print(f"    GEX: {result['gex_scaled']:.1f}M")
        print(f"    Vomma: {result['vomma_scaled']:.1f}M")
        print(f"    Signal: {result['signal_strength']:.1f}")
    
    # Save results
    if results:
        df = pd.DataFrame(results)
        output_path = '/home/<USER>/spx_convergence_analysis/reports/quick_convergence_data.csv'
        df.to_csv(output_path, index=False)
        print(f"\nResults saved to {output_path}")
        print(f"Total dates analyzed: {len(results)}")
        
        # Print summary
        print("\nSummary:")
        print(f"Bearish signals: {df['bearish_signal'].sum()}")
        print(f"Bullish signals: {df['bullish_signal'].sum()}")
        print(f"Explosive signals: {df['explosive_signal'].sum()}")
        
        return df
    else:
        print("No results generated")
        return None

if __name__ == "__main__":
    try:
        results_df = quick_convergence_analysis()
        print("\n=== Quick analysis completed ===")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()

