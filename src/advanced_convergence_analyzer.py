"""
Advanced Convergence Analysis Engine
Implements rigorous statistical and mathematical analysis of options convergence patterns
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats
from scipy.optimize import minimize
import warnings
from analytics_engine import GreeksCalculator
warnings.filterwarnings('ignore')

from data_loader import OptionsDataLoader, SPXDataLoader
from analytics_engine import GreeksCalculator, PortfolioGreeksCalculator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedConvergenceAnalyzer:
    """
    Advanced convergence analyzer with rigorous statistical methods
    """
    
    def __init__(self, data_loader: OptionsDataLoader, greeks_calc: GreeksCalculator = None):
        """
        Initialize advanced convergence analyzer

        Args:
            data_loader: Instance of OptionsDataLoader
            greeks_calc: Optional GreeksCalculator instance
        """
        self.data_loader = data_loader
        self.greeks_calc = greeks_calc if greeks_calc is not None else GreeksCalculator()

        # Get ticker from data loader
        self.ticker = getattr(data_loader, 'ticker', 'SPX')
        self.portfolio_calc = PortfolioGreeksCalculator(self.greeks_calc, self.ticker)

        # Load data for forward-looking analysis
        self.data = self.data_loader.load_raw_data()

    def _get_close_price_column(self, df):
        """Get the appropriate close price column name for the ticker"""
        close_col = f'{self.ticker.lower()}_close'
        if close_col in df.columns:
            return close_col

        # Fallback to common column names
        possible_cols = [f'{self.ticker.lower()}_close', 'close', 'underlying_price', 'spot_price']
        for col in possible_cols:
            if col in df.columns:
                return col

        raise ValueError(f"Could not find close price column for ticker {self.ticker}")
        
    def analyze_forward_convergence(self, target_date: str) -> Dict:
        """
        FORWARD-LOOKING convergence analysis focusing on future strike convergence
        Analyzes current open interest and predicts convergence at future expiration dates

        Args:
            target_date: Future expiration date or analysis target date (YYYY-MM-DD format)

        Returns:
            Dict containing forward-looking convergence metrics and strike targets
        """
        from datetime import datetime

        # Get the most recent trading data (current positions)
        latest_date = self.data['date'].max()
        current_positions = self.data[self.data['date'] == latest_date].copy()

        # Parse target date
        target_dt = pd.to_datetime(target_date)

        # Convert expiry date column to datetime
        current_positions['expiry_date'] = pd.to_datetime(current_positions['Expiry Date'])

        # Focus on options that will be active at the target date
        # For expiration analysis: options expiring on target date
        # For future date analysis: options still active at target date
        if target_dt.date() in current_positions['expiry_date'].dt.date.values:
            # This is an expiration date - analyze convergence pressure
            target_options = current_positions[current_positions['expiry_date'].dt.date == target_dt.date()].copy()
            analysis_type = 'EXPIRATION_CONVERGENCE'
        else:
            # This is a future trading date - analyze options still active
            target_options = current_positions[current_positions['expiry_date'] > target_dt].copy()
            analysis_type = 'FORWARD_CONVERGENCE'

        if len(target_options) == 0:
            return {
                'analysis_type': 'NO_DATA',
                'date': target_date,
                'error': 'No options data available for target date'
            }

        # Calculate current underlying price
        close_col = self._get_close_price_column(target_options)
        current_price = target_options[close_col].iloc[0]

        # Calculate moneyness and identify key strike levels
        target_options['moneyness'] = current_price / target_options['Strike']

        # Find strikes with highest open interest (convergence magnets)
        strike_oi = target_options.groupby('Strike').agg({
            'Open Interest': 'sum',
            'Gamma': 'sum',
            'Delta': 'sum',
            'Volume': 'sum'
        }).sort_values('Open Interest', ascending=False)

        # Identify ATM and high-OI strikes as convergence targets
        atm_strikes = target_options[abs(target_options['moneyness'] - 1.0) < 0.05]['Strike'].unique()
        high_oi_strikes = strike_oi.head(10).index.tolist()  # Top 10 OI strikes

        # Calculate convergence pressure metrics
        total_gamma_exposure = (target_options['Gamma'] * target_options['Open Interest']).sum()
        total_open_interest = target_options['Open Interest'].sum()

        # Calculate pin risk for key strikes
        pin_risk_strikes = {}
        for strike in high_oi_strikes[:5]:  # Top 5 strikes
            strike_data = target_options[target_options['Strike'] == strike]
            if len(strike_data) > 0:
                strike_oi = strike_data['Open Interest'].sum()
                strike_gamma = strike_data['Gamma'].sum()
                distance_from_current = abs(strike - current_price) / current_price

                pin_risk_strikes[strike] = {
                    'open_interest': int(strike_oi),
                    'gamma_exposure': round(strike_gamma * strike_oi, 2),
                    'distance_pct': round(distance_from_current * 100, 2),
                    'pin_strength': round(strike_oi / distance_from_current if distance_from_current > 0 else 0, 2)
                }

        convergence_pressure = self._calculate_forward_convergence_pressure(target_options, current_price)
        days_to_target = (target_dt - pd.to_datetime(latest_date)).days

        # Calculate charm exposure for weekend decay
        charm_exposure = self._calculate_charm_exposure(target_options, current_price, days_to_target)

        results = {
            'analysis_type': analysis_type,
            'date': target_date,
            'current_price': round(current_price, 2),
            'convergence_pressure': convergence_pressure,
            'days_to_target': days_to_target,
            'charm_exposure': charm_exposure,
            'key_strikes': self._get_key_strikes(target_options, current_price)
        }

        # Add forward-looking signal calculations
        results.update(self._calculate_forward_signals(convergence_pressure, total_gamma_exposure, days_to_target))

        return results

    def _calculate_forward_signals(self, convergence_pressure: float, gamma_exposure: float, days_to_target: int) -> Dict:
        """
        Calculate bullish/bearish/explosive signals for forward-looking analysis
        Based on convergence pressure, gamma exposure, and time to target
        """
        signals = {}

        # Normalize gamma exposure (typical range 0-1000)
        normalized_gamma = min(gamma_exposure / 1000, 1.0)

        # Bearish signal: High convergence pressure + High gamma exposure + Near-term expiry
        # Indicates strong pin risk and potential downward pressure
        bearish_threshold = 0.75
        gamma_threshold = 0.3
        time_factor = max(0, (21 - days_to_target) / 21)  # Higher weight for near-term

        bearish_score = (convergence_pressure * 0.5) + (normalized_gamma * 0.3) + (time_factor * 0.2)
        signals['bearish_signal'] = 1 if bearish_score > bearish_threshold else 0

        # Bullish signal: Moderate convergence pressure + Lower gamma exposure + Medium-term
        # Indicates controlled convergence with upward potential
        bullish_threshold = 0.6
        bullish_score = convergence_pressure * (1 - normalized_gamma) * (1 - time_factor)
        signals['bullish_signal'] = 1 if (bullish_score > bullish_threshold and
                                         convergence_pressure > 0.5 and
                                         normalized_gamma < 0.5) else 0

        # Explosive signal: Very high convergence pressure regardless of other factors
        # Indicates potential for significant price movement
        explosive_threshold = 0.85
        signals['explosive_signal'] = 1 if convergence_pressure > explosive_threshold else 0

        # Overall signal strength (0-10 scale)
        signals['signal_strength'] = min(
            (convergence_pressure * 5) + (normalized_gamma * 3) + (time_factor * 2),
            10.0
        )

        return signals

    def _calculate_charm_exposure(self, options_data: pd.DataFrame, current_price: float, days_to_target: int) -> float:
        """
        Calculate charm exposure using the analytics engine
        Higher charm exposure indicates more significant weekend decay effects
        """
        if options_data.empty:
            return 0.0

        # Focus on ATM options where charm has most impact
        atm_range = current_price * 0.05  # 5% around current price
        strike_col = 'Strike' if 'Strike' in options_data.columns else 'strike_price'

        atm_options = options_data[
            (options_data[strike_col] >= current_price - atm_range) &
            (options_data[strike_col] <= current_price + atm_range)
        ]

        if atm_options.empty:
            return 0.0

        # Calculate charm using analytics engine for each ATM option
        weighted_charm = 0.0
        total_weight = 0.0

        for _, option in atm_options.iterrows():
            try:
                # Get option parameters
                K = option[strike_col]
                T = option.get('tte', 0.1)  # Time to expiration

                # Get implied volatility
                iv = option.get('iv_mid', option.get('Bid Implied Volatility', 0.2))
                if pd.isna(iv) or iv <= 0:
                    iv = 0.2  # Default volatility

                # Determine option type
                option_type = 'call' if option.get('Call/Put', 'C') == 'C' else 'put'

                # Calculate charm using analytics engine
                greeks = self.greeks_calc.calculate_second_order_greeks(
                    S=current_price, K=K, T=T, sigma=iv, option_type=option_type
                )

                # Weight by open interest and proximity to current price
                distance_weight = 1 / (1 + abs(K - current_price) / current_price)
                oi_weight = option.get('Open Interest', 0) / 1000  # Normalize OI
                weight = distance_weight * oi_weight

                weighted_charm += abs(greeks['charm']) * weight
                total_weight += weight

            except Exception as e:
                # Skip problematic options
                continue

        if total_weight == 0:
            return 0.0

        # Scale by time factor - charm builds over weekends
        weekend_factor = 1.5 if days_to_target <= 7 else 1.0

        return round((weighted_charm / total_weight) * weekend_factor, 4)

    def _get_key_strikes(self, options_data: pd.DataFrame, current_price: float) -> Dict:
        """
        Get only the most important strikes - top 3 by open interest near current price
        """
        if options_data.empty:
            return {}

        # Focus on strikes within 10% of current price
        price_range = current_price * 0.1

        # Handle different column names for strike price
        strike_col = 'Strike' if 'Strike' in options_data.columns else 'strike_price'

        relevant_options = options_data[
            (options_data[strike_col] >= current_price - price_range) &
            (options_data[strike_col] <= current_price + price_range)
        ]

        if relevant_options.empty:
            return {}

        # Get top 3 strikes by open interest
        oi_col = 'Open Interest' if 'Open Interest' in relevant_options.columns else 'open_interest'
        top_strikes = relevant_options.nlargest(3, oi_col)

        key_strikes = {}
        for _, option in top_strikes.iterrows():
            strike = option[strike_col]
            distance_pct = abs(strike - current_price) / current_price * 100
            key_strikes[strike] = {
                'open_interest': int(option[oi_col]),
                'distance_pct': round(distance_pct, 1)
            }

        return key_strikes

    def analyze_gamma_convergence(self, target_date: str, dte_threshold: int = 7) -> Dict:
        """
        Rigorous gamma convergence analysis for near-expiry options
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            dte_threshold: Days to expiration threshold for analysis
            
        Returns:
            Dictionary with comprehensive gamma convergence metrics
        """
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            return {}
            
        # Filter for near-expiry options
        options_data['dte'] = (pd.to_datetime(options_data['Expiry Date']) - 
                              pd.to_datetime(options_data['date'])).dt.days
        near_expiry = options_data[options_data['dte'] <= dte_threshold].copy()
        
        if len(near_expiry) < 50:  # Need sufficient data
            return {}
            
        # Calculate moneyness and categorize
        close_col = self._get_close_price_column(near_expiry)
        near_expiry['moneyness'] = near_expiry[close_col] / near_expiry['Strike']
        near_expiry['abs_moneyness'] = abs(near_expiry['moneyness'] - 1.0)
        
        # Focus on ATM options (highest gamma)
        atm_options = near_expiry[near_expiry['abs_moneyness'] <= 0.05].copy()
        
        if len(atm_options) < 10:
            return {}
            
        # Gamma convergence metrics
        results = {
            'analysis_type': 'GAMMA_CONVERGENCE',
            'date': target_date,
            'total_contracts': len(near_expiry),
            'atm_contracts': len(atm_options),
            'avg_dte': near_expiry['dte'].mean(),
            
            # Gamma concentration analysis
            'total_gamma_exposure': atm_options['Gamma'].sum(),
            'gamma_concentration_ratio': len(atm_options) / len(near_expiry),
            'max_gamma_strike': atm_options.loc[atm_options['Gamma'].idxmax(), 'Strike'],
            'gamma_weighted_strike': np.average(atm_options['Strike'], weights=atm_options['Gamma']),
            
            # Statistical measures
            'gamma_skewness': stats.skew(atm_options['Gamma']),
            'gamma_kurtosis': stats.kurtosis(atm_options['Gamma']),
            'gamma_cv': atm_options['Gamma'].std() / atm_options['Gamma'].mean(),
            
            # Convergence pressure indicators
            'pin_risk_indicator': self._calculate_pin_risk(atm_options),
            'gamma_imbalance': self._calculate_gamma_imbalance(atm_options),
            'convergence_pressure': self._calculate_convergence_pressure(atm_options)
        }
        
        return results
    
    def analyze_theta_decay_convergence(self, target_date: str, dte_range: Tuple[int, int] = (8, 21)) -> Dict:
        """
        Analyze theta decay convergence patterns
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            dte_range: Tuple of (min_dte, max_dte) for analysis
            
        Returns:
            Dictionary with theta decay convergence metrics
        """
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            return {}
            
        # Filter for theta-sensitive options
        options_data['dte'] = (pd.to_datetime(options_data['Expiry Date']) - 
                              pd.to_datetime(options_data['date'])).dt.days
        theta_sensitive = options_data[
            (options_data['dte'] >= dte_range[0]) & 
            (options_data['dte'] <= dte_range[1])
        ].copy()
        
        if len(theta_sensitive) < 50:
            return {}
            
        # Calculate moneyness
        close_col = self._get_close_price_column(theta_sensitive)
        theta_sensitive['moneyness'] = theta_sensitive[close_col] / theta_sensitive['Strike']
        
        # Theta decay analysis
        results = {
            'analysis_type': 'THETA_DECAY_CONVERGENCE',
            'date': target_date,
            'total_contracts': len(theta_sensitive),
            'avg_dte': theta_sensitive['dte'].mean(),
            
            # Theta exposure analysis
            'total_theta_exposure': theta_sensitive['Theta'].sum(),
            'theta_per_contract': theta_sensitive['Theta'].mean(),
            'theta_concentration': theta_sensitive['Theta'].std() / abs(theta_sensitive['Theta'].mean()),
            
            # Time decay acceleration
            'theta_acceleration': self._calculate_theta_acceleration(theta_sensitive),
            'time_decay_pressure': abs(theta_sensitive['Theta'].sum()) / len(theta_sensitive),
            
            # Moneyness impact on theta
            'atm_theta_ratio': self._calculate_atm_theta_ratio(theta_sensitive),
            'theta_moneyness_correlation': theta_sensitive['Theta'].corr(theta_sensitive['moneyness'])
        }
        
        return results
    
    def analyze_vega_convergence(self, target_date: str, dte_range: Tuple[int, int] = (22, 90)) -> Dict:
        """
        Analyze vega convergence and volatility sensitivity patterns
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            dte_range: Tuple of (min_dte, max_dte) for analysis
            
        Returns:
            Dictionary with vega convergence metrics
        """
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            return {}
            
        # Filter for vega-sensitive options
        options_data['dte'] = (pd.to_datetime(options_data['Expiry Date']) - 
                              pd.to_datetime(options_data['date'])).dt.days
        vega_sensitive = options_data[
            (options_data['dte'] >= dte_range[0]) & 
            (options_data['dte'] <= dte_range[1])
        ].copy()
        
        if len(vega_sensitive) < 50:
            return {}
            
        # Calculate moneyness and implied volatility metrics
        close_col = self._get_close_price_column(vega_sensitive)
        vega_sensitive['moneyness'] = vega_sensitive[close_col] / vega_sensitive['Strike']
        vega_sensitive['mid_iv'] = (vega_sensitive['Bid Implied Volatility'] + 
                                   vega_sensitive['Ask Implied Volatility']) / 2
        
        # Remove invalid IV data
        vega_sensitive = vega_sensitive[vega_sensitive['mid_iv'] > 0].copy()
        
        if len(vega_sensitive) < 30:
            return {}
            
        # Vega convergence analysis
        results = {
            'analysis_type': 'VEGA_CONVERGENCE',
            'date': target_date,
            'total_contracts': len(vega_sensitive),
            'avg_dte': vega_sensitive['dte'].mean(),
            
            # Vega exposure analysis
            'total_vega_exposure': vega_sensitive['Vega'].sum(),
            'vega_per_contract': vega_sensitive['Vega'].mean(),
            'vega_concentration': vega_sensitive['Vega'].std() / vega_sensitive['Vega'].mean(),
            
            # Implied volatility analysis
            'avg_implied_vol': vega_sensitive['mid_iv'].mean(),
            'iv_skew': self._calculate_iv_skew(vega_sensitive),
            'iv_term_structure': self._calculate_iv_term_structure(vega_sensitive),
            
            # Vega-IV relationship
            'vega_iv_correlation': vega_sensitive['Vega'].corr(vega_sensitive['mid_iv']),
            'vol_risk_premium': self._calculate_vol_risk_premium(vega_sensitive)
        }
        
        return results
    
    def _calculate_pin_risk(self, atm_options: pd.DataFrame) -> float:
        """Calculate pin risk indicator for gamma convergence"""
        if len(atm_options) == 0:
            return 0.0
            
        # Pin risk is higher when there's concentration around specific strikes
        strike_gamma = atm_options.groupby('Strike')['Gamma'].sum()
        max_gamma_concentration = strike_gamma.max() / strike_gamma.sum()
        
        return max_gamma_concentration
    
    def _calculate_gamma_imbalance(self, atm_options: pd.DataFrame) -> float:
        """Calculate gamma imbalance between calls and puts"""
        if len(atm_options) == 0:
            return 0.0
            
        calls = atm_options[atm_options['Call/Put'] == 'c']
        puts = atm_options[atm_options['Call/Put'] == 'p']
        
        call_gamma = calls['Gamma'].sum() if len(calls) > 0 else 0
        put_gamma = puts['Gamma'].sum() if len(puts) > 0 else 0
        
        total_gamma = call_gamma + put_gamma
        if total_gamma == 0:
            return 0.0
            
        return abs(call_gamma - put_gamma) / total_gamma
    
    def _calculate_forward_convergence_pressure(self, options_data: pd.DataFrame, current_price: float) -> float:
        """
        Calculate forward-looking convergence pressure based on current open interest
        and distance from current price to high-OI strikes
        """
        if len(options_data) == 0:
            return 0.0

        # Group by strike and calculate total open interest
        strike_oi = options_data.groupby('Strike')['Open Interest'].sum()

        # Calculate weighted pressure based on OI and distance from current price
        total_pressure = 0.0
        total_weight = 0.0

        for strike, oi in strike_oi.items():
            distance = abs(strike - current_price) / current_price
            # Closer strikes have higher pressure, weighted by open interest
            if distance < 0.1:  # Within 10% of current price
                pressure_weight = oi / (1 + distance * 10)  # Inverse distance weighting
                total_pressure += pressure_weight
                total_weight += oi

        return round(total_pressure / total_weight if total_weight > 0 else 0.0, 4)

    def _calculate_convergence_pressure(self, atm_options: pd.DataFrame) -> float:
        """Calculate overall convergence pressure"""
        if len(atm_options) == 0:
            return 0.0

        # Convergence pressure increases with gamma concentration and decreases with DTE
        avg_gamma = atm_options['Gamma'].mean()
        avg_dte = atm_options['dte'].mean()
        
        # Normalize by typical values
        pressure = (avg_gamma * 1000) / max(avg_dte, 1)
        
        return min(pressure, 10.0)  # Cap at reasonable level
    
    def _calculate_theta_acceleration(self, theta_options: pd.DataFrame) -> float:
        """Calculate theta decay acceleration"""
        if len(theta_options) == 0:
            return 0.0
            
        # Theta acceleration is higher for shorter DTE
        theta_options['theta_per_dte'] = abs(theta_options['Theta']) / theta_options['dte']
        
        return theta_options['theta_per_dte'].mean()
    
    def _calculate_atm_theta_ratio(self, theta_options: pd.DataFrame) -> float:
        """Calculate ratio of ATM theta to total theta"""
        if len(theta_options) == 0:
            return 0.0
            
        atm_mask = abs(theta_options['moneyness'] - 1.0) <= 0.05
        atm_theta = theta_options[atm_mask]['Theta'].sum()
        total_theta = theta_options['Theta'].sum()
        
        if total_theta == 0:
            return 0.0
            
        return abs(atm_theta / total_theta)
    
    def _calculate_iv_skew(self, vega_options: pd.DataFrame) -> float:
        """Calculate implied volatility skew"""
        if len(vega_options) < 10:
            return 0.0
            
        # Simple skew: OTM puts vs OTM calls
        otm_puts = vega_options[(vega_options['Call/Put'] == 'p') & (vega_options['moneyness'] < 0.95)]
        otm_calls = vega_options[(vega_options['Call/Put'] == 'c') & (vega_options['moneyness'] > 1.05)]
        
        if len(otm_puts) == 0 or len(otm_calls) == 0:
            return 0.0
            
        put_iv = otm_puts['mid_iv'].mean()
        call_iv = otm_calls['mid_iv'].mean()
        
        return put_iv - call_iv
    
    def _calculate_iv_term_structure(self, vega_options: pd.DataFrame) -> float:
        """Calculate implied volatility term structure slope"""
        if len(vega_options) < 10:
            return 0.0
            
        # Correlation between DTE and IV
        return vega_options['dte'].corr(vega_options['mid_iv'])
    
    def _calculate_vol_risk_premium(self, vega_options: pd.DataFrame) -> float:
        """Calculate volatility risk premium indicator"""
        if len(vega_options) == 0:
            return 0.0
            
        # Simple proxy: difference between high and low IV options
        iv_range = vega_options['mid_iv'].max() - vega_options['mid_iv'].min()
        
        return iv_range

    def run_comprehensive_analysis(self, target_date: str) -> Dict:
        """
        Run comprehensive convergence analysis combining all methods

        Args:
            target_date: Date string in YYYY-MM-DD format

        Returns:
            Dictionary with comprehensive analysis results
        """
        options_data = self.data_loader.get_data_by_date(target_date)

        if options_data.empty:
            return {}

        # Run all analysis types
        gamma_results = self.analyze_gamma_convergence(target_date)
        theta_results = self.analyze_theta_decay_convergence(target_date)
        vega_results = self.analyze_vega_convergence(target_date)

        # Prefix results to avoid conflicts
        prefixed_gamma = {f'gamma_{k}': v for k, v in gamma_results.items() if k not in ['date', 'analysis_type']}
        prefixed_theta = {f'theta_{k}': v for k, v in theta_results.items() if k not in ['date', 'analysis_type']}
        prefixed_vega = {f'vega_{k}': v for k, v in vega_results.items() if k not in ['date', 'analysis_type']}

        # Combine all results
        combined_results = {
            'analysis_type': 'COMPREHENSIVE_CONVERGENCE',
            'date': target_date,
            **prefixed_gamma,
            **prefixed_theta,
            **prefixed_vega
        }

        # Add overall portfolio Greeks
        portfolio_greeks = self.portfolio_calc.calculate_portfolio_greeks(options_data)
        combined_results.update(portfolio_greeks)

        # Add scaled columns for compatibility with visualization/reporting
        combined_results.update(self._add_scaled_columns(combined_results, options_data))

        # Add signal calculations for compatibility
        combined_results.update(self._calculate_signals(combined_results))

        return combined_results

    def _add_scaled_columns(self, results: Dict, options_data: pd.DataFrame) -> Dict:
        """Add scaled columns for compatibility with existing visualization code"""
        scaled_cols = {}

        # Scale Greeks to match expected format (in millions/thousands)
        if 'total_charm' in results:
            scaled_cols['charm_scaled'] = results['total_charm'] / 1000  # Convert to thousands
        else:
            scaled_cols['charm_scaled'] = 0

        if 'total_vanna' in results:
            scaled_cols['vanna_scaled'] = results['total_vanna'] / 1000  # Convert to thousands
        else:
            scaled_cols['vanna_scaled'] = 0

        if 'gex' in results:
            scaled_cols['gex_scaled'] = results['gex'] / 1000000  # Convert to millions
        else:
            scaled_cols['gex_scaled'] = 0

        if 'total_vomma' in results:
            scaled_cols['vomma_scaled'] = results['total_vomma'] / 1000000  # Convert to millions
        else:
            scaled_cols['vomma_scaled'] = 0

        # Calculate VolmBS (Volume-weighted flow)
        call_volume = options_data[options_data['Call/Put'] == 'c']['Volume'].sum()
        put_volume = options_data[options_data['Call/Put'] == 'p']['Volume'].sum()
        volm_bs = call_volume - put_volume
        scaled_cols['volm_bs'] = volm_bs
        scaled_cols['volm_bs_scaled'] = volm_bs / 1000  # Convert to thousands

        # Calculate VxOI (Volatility x Open Interest proxy)
        total_oi = options_data['Open Interest'].sum()
        if total_oi > 0:
            options_data['mid_iv'] = (options_data['Bid Implied Volatility'] +
                                    options_data['Ask Implied Volatility']) / 2
            valid_iv = options_data[options_data['mid_iv'] > 0]
            if len(valid_iv) > 0:
                weighted_iv = (valid_iv['mid_iv'] * valid_iv['Open Interest']).sum() / valid_iv['Open Interest'].sum()
                vx_oi = weighted_iv * total_oi / 1000  # Scale for readability
            else:
                vx_oi = 0
        else:
            vx_oi = 0

        scaled_cols['vx_oi'] = vx_oi
        scaled_cols['vx_oi_scaled'] = vx_oi / 1000000  # Convert to millions

        return scaled_cols

    def _calculate_signals(self, results: Dict) -> Dict:
        """Calculate convergence signals for compatibility"""
        signals = {}

        # Simple signal calculation based on convergence pressure and Greeks
        convergence_pressure = results.get('gamma_convergence_pressure', 0)
        theta_pressure = results.get('theta_time_decay_pressure', 0)

        # Bearish signal: high convergence pressure + high theta decay
        bearish_threshold = 0.4
        signals['bearish_signal'] = 1 if (convergence_pressure > bearish_threshold and theta_pressure > 2.0) else 0

        # Bullish signal: moderate convergence pressure + low theta decay
        bullish_threshold = 0.3
        signals['bullish_signal'] = 1 if (convergence_pressure > bullish_threshold and theta_pressure < 1.5) else 0

        # Explosive signal: very high convergence pressure
        explosive_threshold = 0.6
        signals['explosive_signal'] = 1 if convergence_pressure > explosive_threshold else 0

        # Overall signal strength
        signals['signal_strength'] = min(convergence_pressure + (theta_pressure / 10), 1.0)

        return signals
