"""
Advanced Convergence Analysis Engine
Implements rigorous statistical and mathematical analysis of options convergence patterns
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

from data_loader import SPXDataLoader
from analytics_engine import GreeksCalculator, PortfolioGreeksCalculator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedConvergenceAnalyzer:
    """
    Advanced convergence analyzer with rigorous statistical methods
    """
    
    def __init__(self, data_loader: SPXDataLoader, greeks_calc: GreeksCalculator = None):
        """
        Initialize advanced convergence analyzer
        
        Args:
            data_loader: Instance of SPXDataLoader
            greeks_calc: Optional GreeksCalculator instance
        """
        self.data_loader = data_loader
        self.greeks_calc = greeks_calc if greeks_calc is not None else GreeksCalculator()
        self.portfolio_calc = PortfolioGreeksCalculator(self.greeks_calc)
        
    def analyze_gamma_convergence(self, target_date: str, dte_threshold: int = 7) -> Dict:
        """
        Rigorous gamma convergence analysis for near-expiry options
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            dte_threshold: Days to expiration threshold for analysis
            
        Returns:
            Dictionary with comprehensive gamma convergence metrics
        """
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            return {}
            
        # Filter for near-expiry options
        options_data['dte'] = (pd.to_datetime(options_data['Expiry Date']) - 
                              pd.to_datetime(options_data['date'])).dt.days
        near_expiry = options_data[options_data['dte'] <= dte_threshold].copy()
        
        if len(near_expiry) < 50:  # Need sufficient data
            return {}
            
        # Calculate moneyness and categorize
        near_expiry['moneyness'] = near_expiry['spx_close'] / near_expiry['Strike']
        near_expiry['abs_moneyness'] = abs(near_expiry['moneyness'] - 1.0)
        
        # Focus on ATM options (highest gamma)
        atm_options = near_expiry[near_expiry['abs_moneyness'] <= 0.05].copy()
        
        if len(atm_options) < 10:
            return {}
            
        # Gamma convergence metrics
        results = {
            'analysis_type': 'GAMMA_CONVERGENCE',
            'date': target_date,
            'total_contracts': len(near_expiry),
            'atm_contracts': len(atm_options),
            'avg_dte': near_expiry['dte'].mean(),
            
            # Gamma concentration analysis
            'total_gamma_exposure': atm_options['Gamma'].sum(),
            'gamma_concentration_ratio': len(atm_options) / len(near_expiry),
            'max_gamma_strike': atm_options.loc[atm_options['Gamma'].idxmax(), 'Strike'],
            'gamma_weighted_strike': np.average(atm_options['Strike'], weights=atm_options['Gamma']),
            
            # Statistical measures
            'gamma_skewness': stats.skew(atm_options['Gamma']),
            'gamma_kurtosis': stats.kurtosis(atm_options['Gamma']),
            'gamma_cv': atm_options['Gamma'].std() / atm_options['Gamma'].mean(),
            
            # Convergence pressure indicators
            'pin_risk_indicator': self._calculate_pin_risk(atm_options),
            'gamma_imbalance': self._calculate_gamma_imbalance(atm_options),
            'convergence_pressure': self._calculate_convergence_pressure(atm_options)
        }
        
        return results
    
    def analyze_theta_decay_convergence(self, target_date: str, dte_range: Tuple[int, int] = (8, 21)) -> Dict:
        """
        Analyze theta decay convergence patterns
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            dte_range: Tuple of (min_dte, max_dte) for analysis
            
        Returns:
            Dictionary with theta decay convergence metrics
        """
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            return {}
            
        # Filter for theta-sensitive options
        options_data['dte'] = (pd.to_datetime(options_data['Expiry Date']) - 
                              pd.to_datetime(options_data['date'])).dt.days
        theta_sensitive = options_data[
            (options_data['dte'] >= dte_range[0]) & 
            (options_data['dte'] <= dte_range[1])
        ].copy()
        
        if len(theta_sensitive) < 50:
            return {}
            
        # Calculate moneyness
        theta_sensitive['moneyness'] = theta_sensitive['spx_close'] / theta_sensitive['Strike']
        
        # Theta decay analysis
        results = {
            'analysis_type': 'THETA_DECAY_CONVERGENCE',
            'date': target_date,
            'total_contracts': len(theta_sensitive),
            'avg_dte': theta_sensitive['dte'].mean(),
            
            # Theta exposure analysis
            'total_theta_exposure': theta_sensitive['Theta'].sum(),
            'theta_per_contract': theta_sensitive['Theta'].mean(),
            'theta_concentration': theta_sensitive['Theta'].std() / abs(theta_sensitive['Theta'].mean()),
            
            # Time decay acceleration
            'theta_acceleration': self._calculate_theta_acceleration(theta_sensitive),
            'time_decay_pressure': abs(theta_sensitive['Theta'].sum()) / len(theta_sensitive),
            
            # Moneyness impact on theta
            'atm_theta_ratio': self._calculate_atm_theta_ratio(theta_sensitive),
            'theta_moneyness_correlation': theta_sensitive['Theta'].corr(theta_sensitive['moneyness'])
        }
        
        return results
    
    def analyze_vega_convergence(self, target_date: str, dte_range: Tuple[int, int] = (22, 90)) -> Dict:
        """
        Analyze vega convergence and volatility sensitivity patterns
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            dte_range: Tuple of (min_dte, max_dte) for analysis
            
        Returns:
            Dictionary with vega convergence metrics
        """
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            return {}
            
        # Filter for vega-sensitive options
        options_data['dte'] = (pd.to_datetime(options_data['Expiry Date']) - 
                              pd.to_datetime(options_data['date'])).dt.days
        vega_sensitive = options_data[
            (options_data['dte'] >= dte_range[0]) & 
            (options_data['dte'] <= dte_range[1])
        ].copy()
        
        if len(vega_sensitive) < 50:
            return {}
            
        # Calculate moneyness and implied volatility metrics
        vega_sensitive['moneyness'] = vega_sensitive['spx_close'] / vega_sensitive['Strike']
        vega_sensitive['mid_iv'] = (vega_sensitive['Bid Implied Volatility'] + 
                                   vega_sensitive['Ask Implied Volatility']) / 2
        
        # Remove invalid IV data
        vega_sensitive = vega_sensitive[vega_sensitive['mid_iv'] > 0].copy()
        
        if len(vega_sensitive) < 30:
            return {}
            
        # Vega convergence analysis
        results = {
            'analysis_type': 'VEGA_CONVERGENCE',
            'date': target_date,
            'total_contracts': len(vega_sensitive),
            'avg_dte': vega_sensitive['dte'].mean(),
            
            # Vega exposure analysis
            'total_vega_exposure': vega_sensitive['Vega'].sum(),
            'vega_per_contract': vega_sensitive['Vega'].mean(),
            'vega_concentration': vega_sensitive['Vega'].std() / vega_sensitive['Vega'].mean(),
            
            # Implied volatility analysis
            'avg_implied_vol': vega_sensitive['mid_iv'].mean(),
            'iv_skew': self._calculate_iv_skew(vega_sensitive),
            'iv_term_structure': self._calculate_iv_term_structure(vega_sensitive),
            
            # Vega-IV relationship
            'vega_iv_correlation': vega_sensitive['Vega'].corr(vega_sensitive['mid_iv']),
            'vol_risk_premium': self._calculate_vol_risk_premium(vega_sensitive)
        }
        
        return results
    
    def _calculate_pin_risk(self, atm_options: pd.DataFrame) -> float:
        """Calculate pin risk indicator for gamma convergence"""
        if len(atm_options) == 0:
            return 0.0
            
        # Pin risk is higher when there's concentration around specific strikes
        strike_gamma = atm_options.groupby('Strike')['Gamma'].sum()
        max_gamma_concentration = strike_gamma.max() / strike_gamma.sum()
        
        return max_gamma_concentration
    
    def _calculate_gamma_imbalance(self, atm_options: pd.DataFrame) -> float:
        """Calculate gamma imbalance between calls and puts"""
        if len(atm_options) == 0:
            return 0.0
            
        calls = atm_options[atm_options['Call/Put'] == 'c']
        puts = atm_options[atm_options['Call/Put'] == 'p']
        
        call_gamma = calls['Gamma'].sum() if len(calls) > 0 else 0
        put_gamma = puts['Gamma'].sum() if len(puts) > 0 else 0
        
        total_gamma = call_gamma + put_gamma
        if total_gamma == 0:
            return 0.0
            
        return abs(call_gamma - put_gamma) / total_gamma
    
    def _calculate_convergence_pressure(self, atm_options: pd.DataFrame) -> float:
        """Calculate overall convergence pressure"""
        if len(atm_options) == 0:
            return 0.0
            
        # Convergence pressure increases with gamma concentration and decreases with DTE
        avg_gamma = atm_options['Gamma'].mean()
        avg_dte = atm_options['dte'].mean()
        
        # Normalize by typical values
        pressure = (avg_gamma * 1000) / max(avg_dte, 1)
        
        return min(pressure, 10.0)  # Cap at reasonable level
    
    def _calculate_theta_acceleration(self, theta_options: pd.DataFrame) -> float:
        """Calculate theta decay acceleration"""
        if len(theta_options) == 0:
            return 0.0
            
        # Theta acceleration is higher for shorter DTE
        theta_options['theta_per_dte'] = abs(theta_options['Theta']) / theta_options['dte']
        
        return theta_options['theta_per_dte'].mean()
    
    def _calculate_atm_theta_ratio(self, theta_options: pd.DataFrame) -> float:
        """Calculate ratio of ATM theta to total theta"""
        if len(theta_options) == 0:
            return 0.0
            
        atm_mask = abs(theta_options['moneyness'] - 1.0) <= 0.05
        atm_theta = theta_options[atm_mask]['Theta'].sum()
        total_theta = theta_options['Theta'].sum()
        
        if total_theta == 0:
            return 0.0
            
        return abs(atm_theta / total_theta)
    
    def _calculate_iv_skew(self, vega_options: pd.DataFrame) -> float:
        """Calculate implied volatility skew"""
        if len(vega_options) < 10:
            return 0.0
            
        # Simple skew: OTM puts vs OTM calls
        otm_puts = vega_options[(vega_options['Call/Put'] == 'p') & (vega_options['moneyness'] < 0.95)]
        otm_calls = vega_options[(vega_options['Call/Put'] == 'c') & (vega_options['moneyness'] > 1.05)]
        
        if len(otm_puts) == 0 or len(otm_calls) == 0:
            return 0.0
            
        put_iv = otm_puts['mid_iv'].mean()
        call_iv = otm_calls['mid_iv'].mean()
        
        return put_iv - call_iv
    
    def _calculate_iv_term_structure(self, vega_options: pd.DataFrame) -> float:
        """Calculate implied volatility term structure slope"""
        if len(vega_options) < 10:
            return 0.0
            
        # Correlation between DTE and IV
        return vega_options['dte'].corr(vega_options['mid_iv'])
    
    def _calculate_vol_risk_premium(self, vega_options: pd.DataFrame) -> float:
        """Calculate volatility risk premium indicator"""
        if len(vega_options) == 0:
            return 0.0
            
        # Simple proxy: difference between high and low IV options
        iv_range = vega_options['mid_iv'].max() - vega_options['mid_iv'].min()
        
        return iv_range
