"""
Data Loader Module for SPX Options Data
Handles loading, cleaning, and preprocessing of options data
"""
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OptionsDataLoader:
    """
    Loads and preprocesses options data for any ticker (SPX, VIX, TLT, etc.)
    """

    def __init__(self, data_file_path: str, ticker: str = "SPX"):
        """
        Initialize the data loader

        Args:
            data_file_path: Path to the CSV data file
            ticker: The ticker symbol (SPX, VIX, TLT, etc.)
        """
        self.data_file_path = Path(data_file_path)
        self.ticker = ticker.upper()
        self.raw_data = None
        self.processed_data = None
        
    def load_raw_data(self) -> pd.DataFrame:
        """
        Load raw data from CSV file
        
        Returns:
            DataFrame with raw options data
        """
        try:
            logger.info(f"Loading data from {self.data_file_path}")
            self.raw_data = pd.read_csv(self.data_file_path)
            logger.info(f"Loaded {len(self.raw_data)} rows of data")
            return self.raw_data
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def clean_data(self) -> pd.DataFrame:
        """
        Clean and preprocess the raw data
        
        Returns:
            Cleaned DataFrame
        """
        if self.raw_data is None:
            self.load_raw_data()
            
        df = self.raw_data.copy()
        
        # Convert date columns
        df['date'] = pd.to_datetime(df['date'])
        df['Expiry Date'] = pd.to_datetime(df['Expiry Date'])
        
        # Calculate time to expiration in years
        df['tte'] = (df['Expiry Date'] - df['date']).dt.days / 365.25
        
        # Filter out expired options and very short-term options
        df = df[df['tte'] > 0]
        df = df[df['tte'] <= 2]  # Max 2 years
        
        # Calculate moneyness - use dynamic column name based on ticker
        close_col = f'{self.ticker.lower()}_close'
        if close_col not in df.columns:
            # Fallback to common column names
            possible_cols = [f'{self.ticker.lower()}_close', 'close', 'underlying_price', 'spot_price']
            close_col = next((col for col in possible_cols if col in df.columns), None)
            if close_col is None:
                raise ValueError(f"Could not find close price column for ticker {self.ticker}. Available columns: {list(df.columns)}")

        df['moneyness'] = df['Strike'] / df[close_col]
        
        # Filter reasonable moneyness range
        df = df[(df['moneyness'] >= 0.5) & (df['moneyness'] <= 1.5)]
        
        # Calculate mid prices
        df['mid_price'] = (df['Bid Price'] + df['Ask Price']) / 2
        
        # Filter out options with zero or negative prices
        df = df[df['mid_price'] > 0]
        
        # Calculate implied volatility mid
        df['iv_mid'] = (df['Bid Implied Volatility'] + df['Ask Implied Volatility']) / 2
        
        # Filter out zero IV
        df = df[df['iv_mid'] > 0]
        
        # Add option type indicator
        df['is_call'] = (df['Call/Put'] == 'c').astype(int)
        
        logger.info(f"Cleaned data: {len(df)} rows remaining")
        self.processed_data = df
        return df
    
    def get_data_by_date(self, target_date: str) -> pd.DataFrame:
        """
        Get options data for a specific date
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            
        Returns:
            DataFrame filtered for the target date
        """
        if self.processed_data is None:
            self.clean_data()
            
        target_date = pd.to_datetime(target_date)
        return self.processed_data[self.processed_data['date'] == target_date].copy()
    
    def get_date_range(self) -> Tuple[datetime, datetime]:
        """
        Get the date range of available data
        
        Returns:
            Tuple of (start_date, end_date)
        """
        if self.processed_data is None:
            self.clean_data()
            
        return (
            self.processed_data['date'].min(),
            self.processed_data['date'].max()
        )
    
    def get_available_dates(self) -> List[datetime]:
        """
        Get list of all available dates in the dataset
        
        Returns:
            List of available dates
        """
        if self.processed_data is None:
            self.clean_data()
            
        return sorted(self.processed_data['date'].unique())
    
    def get_spot_prices(self) -> pd.DataFrame:
        """
        Get spot prices by date for the configured ticker

        Returns:
            DataFrame with date and spot price columns
        """
        if self.processed_data is None:
            self.clean_data()

        # Dynamic column names based on ticker
        ticker_lower = self.ticker.lower()
        price_cols = {}

        # Try to find the appropriate columns
        for price_type in ['open', 'high', 'low', 'close']:
            col_name = f'{ticker_lower}_{price_type}'
            if col_name in self.processed_data.columns:
                price_cols[col_name] = 'first'

        if not price_cols:
            # Fallback to generic column names
            generic_cols = ['open', 'high', 'low', 'close', 'underlying_price', 'spot_price']
            for col in generic_cols:
                if col in self.processed_data.columns:
                    price_cols[col] = 'first'

        if not price_cols:
            raise ValueError(f"Could not find price columns for ticker {self.ticker}")

        spot_data = self.processed_data.groupby('date').agg(price_cols).reset_index()

        return spot_data


# Legacy class for backward compatibility
class SPXDataLoader(OptionsDataLoader):
    """
    Legacy SPX data loader - now inherits from OptionsDataLoader
    """
    def __init__(self, data_file_path: str):
        super().__init__(data_file_path, ticker="SPX")
    
    def get_summary_stats(self) -> Dict:
        """
        Get summary statistics of the dataset
        
        Returns:
            Dictionary with summary statistics
        """
        if self.processed_data is None:
            self.clean_data()
            
        df = self.processed_data
        
        stats = {
            'total_rows': len(df),
            'date_range': self.get_date_range(),
            'unique_dates': len(df['date'].unique()),
            'unique_strikes': len(df['Strike'].unique()),
            'call_put_ratio': df['is_call'].mean(),
            'avg_tte': df['tte'].mean(),
            'avg_iv': df['iv_mid'].mean(),
            'avg_volume': df['Volume'].mean(),
            'avg_open_interest': df['Open Interest'].mean()
        }
        
        return stats

