"""
Analytics Engine for Options Greeks Calculation
Calculates all Greeks including higher-order Greeks: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
"""
import numpy as np
import pandas as pd
from scipy.stats import norm
from scipy.optimize import minimize_scalar
import logging
from typing import Dict, Optional
import warnings
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GreeksCalculator:
    """
    Comprehensive Greeks calculation engine using Black-Scholes model
    """
    
    def __init__(self, risk_free_rate: float = 0.05, dividend_yield: float = 0.015):
        """
        Initialize the Greeks calculator
        
        Args:
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield
        """
        self.risk_free_rate = risk_free_rate
        self.dividend_yield = dividend_yield
    
    @staticmethod
    def black_scholes_price(S: float, K: float, T: float, r: float, q: float, 
                           sigma: float, option_type: str) -> float:
        """
        Calculate Black-Scholes option price
        
        Args:
            S: Spot price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            q: Dividend yield
            sigma: Volatility
            option_type: 'call' or 'put'
            
        Returns:
            Option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1 = (np.log(S/K) + (r - q + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        if option_type.lower() == 'call':
            price = S*np.exp(-q*T)*norm.cdf(d1) - K*np.exp(-r*T)*norm.cdf(d2)
        else:
            price = K*np.exp(-r*T)*norm.cdf(-d2) - S*np.exp(-q*T)*norm.cdf(-d1)
            
        return price
    
    def calculate_implied_volatility(self, market_price: float, S: float, K: float, 
                                   T: float, option_type: str) -> float:
        """
        Calculate implied volatility using Brent's method
        
        Args:
            market_price: Market price of the option
            S: Spot price
            K: Strike price
            T: Time to expiration
            option_type: 'call' or 'put'
            
        Returns:
            Implied volatility
        """
        if T <= 0:
            return 0.0
            
        def objective(sigma):
            try:
                bs_price = self.black_scholes_price(S, K, T, self.risk_free_rate, 
                                                  self.dividend_yield, sigma, option_type)
                return abs(bs_price - market_price)
            except:
                return 1e6
        
        try:
            result = minimize_scalar(objective, bounds=(0.001, 5.0), method='bounded')
            return result.x if result.success else 0.2
        except:
            return 0.2
    
    def calculate_first_order_greeks(self, S: float, K: float, T: float, 
                                   sigma: float, option_type: str) -> Dict[str, float]:
        """
        Calculate first-order Greeks: Delta, Vega, Theta, Rho
        
        Args:
            S: Spot price
            K: Strike price
            T: Time to expiration
            sigma: Volatility
            option_type: 'call' or 'put'
            
        Returns:
            Dictionary with first-order Greeks
        """
        if T <= 0:
            return {'delta': 0, 'vega': 0, 'theta': 0, 'rho': 0}
        
        d1 = (np.log(S/K) + (self.risk_free_rate - self.dividend_yield + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        # Delta
        if option_type.lower() == 'call':
            delta = np.exp(-self.dividend_yield*T) * norm.cdf(d1)
        else:
            delta = -np.exp(-self.dividend_yield*T) * norm.cdf(-d1)
        
        # Vega (same for calls and puts)
        vega = S * np.exp(-self.dividend_yield*T) * norm.pdf(d1) * np.sqrt(T) / 100
        
        # Theta
        theta_common = (-S * np.exp(-self.dividend_yield*T) * norm.pdf(d1) * sigma / (2*np.sqrt(T)) 
                       + self.dividend_yield * S * np.exp(-self.dividend_yield*T))
        
        if option_type.lower() == 'call':
            theta = (theta_common * norm.cdf(d1) 
                    - self.risk_free_rate * K * np.exp(-self.risk_free_rate*T) * norm.cdf(d2)) / 365
        else:
            theta = (theta_common * norm.cdf(-d1) 
                    + self.risk_free_rate * K * np.exp(-self.risk_free_rate*T) * norm.cdf(-d2)) / 365
        
        # Rho
        if option_type.lower() == 'call':
            rho = K * T * np.exp(-self.risk_free_rate*T) * norm.cdf(d2) / 100
        else:
            rho = -K * T * np.exp(-self.risk_free_rate*T) * norm.cdf(-d2) / 100
        
        return {
            'delta': delta,
            'vega': vega,
            'theta': theta,
            'rho': rho
        }
    
    def calculate_second_order_greeks(self, S: float, K: float, T: float, 
                                    sigma: float, option_type: str) -> Dict[str, float]:
        """
        Calculate second-order Greeks: Gamma, Vomma, Vanna, Charm
        
        Args:
            S: Spot price
            K: Strike price
            T: Time to expiration
            sigma: Volatility
            option_type: 'call' or 'put'
            
        Returns:
            Dictionary with second-order Greeks
        """
        if T <= 0:
            return {'gamma': 0, 'vomma': 0, 'vanna': 0, 'charm': 0}
        
        d1 = (np.log(S/K) + (self.risk_free_rate - self.dividend_yield + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        # Gamma (same for calls and puts)
        gamma = (np.exp(-self.dividend_yield*T) * norm.pdf(d1)) / (S * sigma * np.sqrt(T))
        
        # Vomma (volatility gamma - second derivative with respect to volatility)
        vomma = (S * np.exp(-self.dividend_yield*T) * norm.pdf(d1) * np.sqrt(T) * d1 * d2) / (sigma * 10000)
        
        # Vanna (cross-derivative: delta with respect to volatility)
        vanna = (-np.exp(-self.dividend_yield*T) * norm.pdf(d1) * d2) / (sigma * 100)
        
        # Charm (delta decay - derivative of delta with respect to time)
        if option_type.lower() == 'call':
            charm = (-self.dividend_yield * np.exp(-self.dividend_yield*T) * norm.cdf(d1)
                    - np.exp(-self.dividend_yield*T) * norm.pdf(d1) * 
                    (2*(self.risk_free_rate - self.dividend_yield)*T - d2*sigma*np.sqrt(T)) / 
                    (2*T*sigma*np.sqrt(T))) / 365
        else:
            charm = (self.dividend_yield * np.exp(-self.dividend_yield*T) * norm.cdf(-d1)
                    - np.exp(-self.dividend_yield*T) * norm.pdf(d1) * 
                    (2*(self.risk_free_rate - self.dividend_yield)*T - d2*sigma*np.sqrt(T)) / 
                    (2*T*sigma*np.sqrt(T))) / 365
        
        return {
            'gamma': gamma,
            'vomma': vomma,
            'vanna': vanna,
            'charm': charm
        }
    
    def calculate_all_greeks(self, S: float, K: float, T: float, 
                           sigma: float, option_type: str) -> Dict[str, float]:
        """
        Calculate all Greeks for an option
        
        Args:
            S: Spot price
            K: Strike price
            T: Time to expiration
            sigma: Volatility
            option_type: 'call' or 'put'
            
        Returns:
            Dictionary with all Greeks
        """
        first_order = self.calculate_first_order_greeks(S, K, T, sigma, option_type)
        second_order = self.calculate_second_order_greeks(S, K, T, sigma, option_type)
        
        # Combine all Greeks
        all_greeks = {**first_order, **second_order}
        
        # Add option price
        all_greeks['price'] = self.black_scholes_price(S, K, T, self.risk_free_rate, 
                                                      self.dividend_yield, sigma, option_type)
        
        return all_greeks

class PortfolioGreeksCalculator:
    """
    Calculate portfolio-level Greeks and aggregate metrics
    """

    def __init__(self, greeks_calculator: GreeksCalculator, ticker: str = "SPX"):
        """
        Initialize portfolio Greeks calculator

        Args:
            greeks_calculator: Instance of GreeksCalculator
            ticker: The ticker symbol being analyzed
        """
        self.greeks_calc = greeks_calculator
        self.ticker = ticker.upper()

        # Determine the close price column name
        self.close_col = f'{ticker.lower()}_close'
    
    def calculate_portfolio_greeks(self, options_df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate portfolio-level Greeks from options DataFrame
        
        Args:
            options_df: DataFrame with options data
            
        Returns:
            Dictionary with portfolio Greeks
        """
        portfolio_greeks = {
            'total_delta': 0,
            'total_gamma': 0,
            'total_vega': 0,
            'total_theta': 0,
            'total_vomma': 0,
            'total_vanna': 0,
            'total_charm': 0,
            'gex': 0,  # Gamma Exposure
            'total_open_interest': 0,
            'total_volume': 0
        }
        
        for _, row in options_df.iterrows():
            # Get option parameters - use dynamic column name
            if self.close_col in row:
                S = row[self.close_col]
            else:
                # Fallback to common column names
                possible_cols = [f'{self.ticker.lower()}_close', 'close', 'underlying_price', 'spot_price']
                S = next((row[col] for col in possible_cols if col in row), None)
                if S is None:
                    raise ValueError(f"Could not find close price column for ticker {self.ticker}")
            K = row['Strike']
            T = row['tte']
            sigma = row['iv_mid']
            option_type = 'call' if row['is_call'] else 'put'
            open_interest = row['Open Interest']
            volume = row['Volume']
            
            # Calculate Greeks for this option
            greeks = self.greeks_calc.calculate_all_greeks(S, K, T, sigma, option_type)
            
            # Weight by open interest
            weight = open_interest
            
            # Accumulate portfolio Greeks
            portfolio_greeks['total_delta'] += greeks['delta'] * weight
            portfolio_greeks['total_gamma'] += greeks['gamma'] * weight
            portfolio_greeks['total_vega'] += greeks['vega'] * weight
            portfolio_greeks['total_theta'] += greeks['theta'] * weight
            portfolio_greeks['total_vomma'] += greeks['vomma'] * weight
            portfolio_greeks['total_vanna'] += greeks['vanna'] * weight
            portfolio_greeks['total_charm'] += greeks['charm'] * weight
            
            # Calculate GEX (Gamma Exposure)
            # GEX = Gamma * Open Interest * Spot^2 * 0.01
            portfolio_greeks['gex'] += greeks['gamma'] * weight * S**2 * 0.01
            
            portfolio_greeks['total_open_interest'] += open_interest
            portfolio_greeks['total_volume'] += volume
        
        return portfolio_greeks
    
    def calculate_gex_by_strike(self, options_df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Gamma Exposure (GEX) by strike level
        
        Args:
            options_df: DataFrame with options data
            
        Returns:
            DataFrame with GEX by strike
        """
        gex_data = []
        
        for strike in sorted(options_df['Strike'].unique()):
            strike_data = options_df[options_df['Strike'] == strike]
            
            total_gex = 0
            for _, row in strike_data.iterrows():
                # Get spot price using dynamic column name
                if self.close_col in row:
                    S = row[self.close_col]
                else:
                    # Fallback to common column names
                    possible_cols = [f'{self.ticker.lower()}_close', 'close', 'underlying_price', 'spot_price']
                    S = next((row[col] for col in possible_cols if col in row), None)
                    if S is None:
                        raise ValueError(f"Could not find close price column for ticker {self.ticker}")
                K = row['Strike']
                T = row['tte']
                sigma = row['iv_mid']
                option_type = 'call' if row['is_call'] else 'put'
                open_interest = row['Open Interest']
                
                greeks = self.greeks_calc.calculate_all_greeks(S, K, T, sigma, option_type)
                gex = greeks['gamma'] * open_interest * S**2 * 0.01
                total_gex += gex
            
            gex_data.append({
                'strike': strike,
                'gex': total_gex,
                'distance_from_spot': (strike - S) / S * 100
            })
        
        return pd.DataFrame(gex_data)

