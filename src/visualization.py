"""
Visualization Module for Convergence Analysis
Creates charts and visualizations for the convergence analysis
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ConvergenceVisualizer:
    """
    Creates visualizations for convergence analysis
    """
    
    def __init__(self, output_dir: str = "reports", ticker: str = "SPX"):
        """
        Initialize visualizer

        Args:
            output_dir: Directory to save charts
            ticker: Ticker symbol for the analysis
        """
        self.output_dir = Path(output_dir) / "charts"
        self.output_dir.mkdir(exist_ok=True)
        self.ticker = ticker
        
        # Chart styling
        self.fig_size = (15, 10)
        self.dpi = 300
        self.colors = {
            'charm': '#1f77b4',
            'vanna': '#ff7f0e', 
            'gex': '#2ca02c',
            'vomma': '#d62728',
            'volm_bs': '#9467bd',
            'vx_oi': '#8c564b',
            'spot': '#e377c2'
        }
    
    def create_convergence_overview_chart(self, convergence_df: pd.DataFrame, 
                                        expected_outcomes: Dict[str, Dict]) -> str:
        """
        Create overview chart showing all convergence dates and Greeks
        
        Args:
            convergence_df: DataFrame with convergence analysis
            expected_outcomes: Expected outcomes from document
            
        Returns:
            Path to saved chart
        """
        fig, axes = plt.subplots(3, 2, figsize=(20, 15))
        fig.suptitle('SPX Options Greeks Convergence Analysis', fontsize=16, fontweight='bold')
        
        # Convert date column to datetime
        convergence_df['date'] = pd.to_datetime(convergence_df['date'])
        dates = convergence_df['date']
        
        # Check if this is forward-looking data
        if 'convergence_pressure' in convergence_df.columns:
            plt.close()
            return self._create_forward_convergence_overview(convergence_df, expected_outcomes)

        # Check if required columns exist for historical analysis
        if 'charm_scaled' not in convergence_df.columns:
            logger.warning("Required columns not found for historical analysis")
            plt.close()
            return ""

        # Chart 1: Charm and Vanna
        ax1 = axes[0, 0]
        ax1.plot(dates, convergence_df['charm_scaled'], 'o-', color=self.colors['charm'],
                linewidth=2, markersize=8, label='Charm (K)')
        ax1_twin = ax1.twinx()
        ax1_twin.plot(dates, convergence_df['vanna_scaled'], 's-', color=self.colors['vanna'],
                     linewidth=2, markersize=8, label='Vanna (K)')
        ax1.set_title('Charm vs Vanna Convergence', fontweight='bold')
        ax1.set_ylabel('Charm (Thousands)', color=self.colors['charm'])
        ax1_twin.set_ylabel('Vanna (Thousands)', color=self.colors['vanna'])
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper left')
        ax1_twin.legend(loc='upper right')
        
        # Chart 2: GEX and Vomma
        ax2 = axes[0, 1]
        ax2.plot(dates, convergence_df['gex_scaled'], 'o-', color=self.colors['gex'], 
                linewidth=2, markersize=8, label='GEX (M)')
        ax2_twin = ax2.twinx()
        ax2_twin.plot(dates, convergence_df['vomma_scaled'], 's-', color=self.colors['vomma'], 
                     linewidth=2, markersize=8, label='Vomma (M)')
        ax2.set_title('GEX vs Vomma Convergence', fontweight='bold')
        ax2.set_ylabel('GEX (Millions)', color=self.colors['gex'])
        ax2_twin.set_ylabel('Vomma (Millions)', color=self.colors['vomma'])
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper left')
        ax2_twin.legend(loc='upper right')
        
        # Chart 3: VolmBS and VxOI
        ax3 = axes[1, 0]
        ax3.plot(dates, convergence_df['volm_bs_scaled'], 'o-', color=self.colors['volm_bs'], 
                linewidth=2, markersize=8, label='VolmBS (K)')
        ax3_twin = ax3.twinx()
        ax3_twin.plot(dates, convergence_df['vx_oi_scaled'], 's-', color=self.colors['vx_oi'], 
                     linewidth=2, markersize=8, label='VxOI (M)')
        ax3.set_title('VolmBS vs VxOI Convergence', fontweight='bold')
        ax3.set_ylabel('VolmBS (Thousands)', color=self.colors['volm_bs'])
        ax3_twin.set_ylabel('VxOI (Millions)', color=self.colors['vx_oi'])
        ax3.grid(True, alpha=0.3)
        ax3.legend(loc='upper left')
        ax3_twin.legend(loc='upper right')
        
        # Chart 4: Signal Strength
        ax4 = axes[1, 1]
        signal_colors = ['red' if x < 0 else 'green' if x > 2 else 'orange' for x in convergence_df['signal_strength']]
        bars = ax4.bar(dates, convergence_df['signal_strength'], color=signal_colors, alpha=0.7)
        ax4.set_title('Convergence Signal Strength', fontweight='bold')
        ax4.set_ylabel('Signal Strength')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax4.grid(True, alpha=0.3)
        
        # Add signal strength legend
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='red', label='Bearish'),
                          Patch(facecolor='orange', label='Bullish'), 
                          Patch(facecolor='green', label='Explosive')]
        ax4.legend(handles=legend_elements)
        
        # Chart 5: SPX Price Movement
        ax5 = axes[2, 0]
        ax5.plot(dates, convergence_df['spot_price'], 'o-', color=self.colors['spot'], 
                linewidth=3, markersize=8, label='SPX Close')
        ax5.set_title('SPX Price During Convergence Dates', fontweight='bold')
        ax5.set_ylabel('SPX Price')
        ax5.grid(True, alpha=0.3)
        ax5.legend()
        
        # Chart 6: Expected vs Actual (placeholder for future enhancement)
        ax6 = axes[2, 1]
        # Create a summary table of convergence patterns
        pattern_data = []
        for _, row in convergence_df.iterrows():
            date_str = row['date'].strftime('%Y-%m-%d')
            if date_str in expected_outcomes:
                expected = expected_outcomes[date_str]['direction']
                pattern_data.append([date_str[:5], expected, f"{row['signal_strength']:.1f}"])
        
        if pattern_data:
            table = ax6.table(cellText=pattern_data,
                            colLabels=['Date', 'Expected', 'Signal'],
                            cellLoc='center',
                            loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1.2, 1.5)
            ax6.axis('off')
            ax6.set_title('Expected vs Calculated Signals', fontweight='bold')
        
        # Format x-axes
        for ax in [ax1, ax2, ax3, ax4, ax5]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=7))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.output_dir / "convergence_overview.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Convergence overview chart saved to {chart_path}")
        return str(chart_path)
    
    def create_greeks_heatmap(self, convergence_df: pd.DataFrame) -> str:
        """
        Create heatmap of Greeks values across convergence dates

        Args:
            convergence_df: DataFrame with convergence analysis

        Returns:
            Path to saved chart
        """
        # Check if this is forward-looking data
        if 'convergence_pressure' in convergence_df.columns:
            return self._create_forward_heatmap(convergence_df)

        # Check if required columns exist for historical analysis
        if 'charm_scaled' not in convergence_df.columns:
            logger.warning("Required columns not found for Greeks heatmap")
            return ""

        # Prepare data for heatmap
        heatmap_data = convergence_df[['date', 'charm_scaled', 'vanna_scaled',
                                     'gex_scaled', 'vomma_scaled', 'volm_bs_scaled',
                                     'vx_oi_scaled']].copy()

        heatmap_data['date'] = pd.to_datetime(heatmap_data['date']).dt.strftime('%m-%d')
        heatmap_data = heatmap_data.set_index('date')
        
        # Rename columns for better display
        heatmap_data.columns = ['Charm (K)', 'Vanna (K)', 'GEX (M)', 
                               'Vomma (M)', 'VolmBS (K)', 'VxOI (M)']
        
        # Create heatmap
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Normalize data for better color representation
        heatmap_data_norm = heatmap_data.copy()
        for col in heatmap_data_norm.columns:
            col_data = heatmap_data_norm[col]
            heatmap_data_norm[col] = (col_data - col_data.mean()) / col_data.std()
        
        sns.heatmap(heatmap_data_norm.T, annot=heatmap_data.T, fmt='.1f', 
                   cmap='RdYlBu_r', center=0, ax=ax, cbar_kws={'label': 'Normalized Values'})
        
        ax.set_title('Greeks Values Heatmap Across Convergence Dates', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Convergence Dates', fontweight='bold')
        ax.set_ylabel('Greeks Metrics', fontweight='bold')
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.output_dir / "greeks_heatmap.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Greeks heatmap saved to {chart_path}")
        return str(chart_path)
    
    def create_pattern_analysis_chart(self, convergence_df: pd.DataFrame) -> str:
        """
        Create chart analyzing convergence patterns
        
        Args:
            convergence_df: DataFrame with convergence analysis
            
        Returns:
            Path to saved chart
        """
        # Check if this is forward-looking data
        if 'convergence_pressure' in convergence_df.columns:
            return self._create_forward_pattern_analysis(convergence_df)

        # Check if required columns exist for historical analysis
        if 'charm_scaled' not in convergence_df.columns:
            logger.warning("Required columns not found for pattern analysis")
            return ""

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Convergence Pattern Analysis', fontsize=16, fontweight='bold')

        # Chart 1: Bearish Pattern Components
        ax1 = axes[0, 0]
        dates = pd.to_datetime(convergence_df['date'])
        
        # Highlight bearish signals
        bearish_dates = dates[convergence_df['bearish_signal'] == 1]
        ax1.scatter(dates, convergence_df['charm_scaled'], c='blue', alpha=0.6, s=60, label='Charm')
        ax1.scatter(dates, convergence_df['gex_scaled'], c='green', alpha=0.6, s=60, label='GEX')
        
        # Highlight bearish convergence dates
        for bd in bearish_dates:
            ax1.axvline(x=bd, color='red', linestyle='--', alpha=0.7)
        
        ax1.set_title('Bearish Pattern: High Charm + High GEX', fontweight='bold')
        ax1.set_ylabel('Values (Scaled)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Chart 2: Bullish Pattern Components  
        ax2 = axes[0, 1]
        bullish_dates = dates[convergence_df['bullish_signal'] == 1]
        ax2.scatter(dates, convergence_df['vomma_scaled'], c='red', alpha=0.6, s=60, label='Vomma')
        ax2.scatter(dates, convergence_df['vanna_scaled'], c='orange', alpha=0.6, s=60, label='Vanna')
        
        # Highlight bullish convergence dates
        for bd in bullish_dates:
            ax2.axvline(x=bd, color='green', linestyle='--', alpha=0.7)
        
        ax2.set_title('Bullish Pattern: Negative Vomma + High Vanna', fontweight='bold')
        ax2.set_ylabel('Values (Scaled)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Chart 3: Explosive Pattern Components
        ax3 = axes[1, 0]
        explosive_dates = dates[convergence_df['explosive_signal'] == 1]
        
        # Create a combined explosive score
        explosive_score = (convergence_df['vomma_scaled'] < -100).astype(int) + \
                         (convergence_df['vanna_scaled'] > 200).astype(int) + \
                         ((convergence_df['gex_scaled'] > 10) & (convergence_df['gex_scaled'] < 200)).astype(int) + \
                         (convergence_df['charm_scaled'] < 0).astype(int)
        
        bars = ax3.bar(dates, explosive_score, alpha=0.7, 
                      color=['darkgreen' if x == 4 else 'lightgreen' if x >= 2 else 'lightcoral' for x in explosive_score])
        
        # Highlight explosive convergence dates
        for ed in explosive_dates:
            ax3.axvline(x=ed, color='purple', linestyle='--', alpha=0.7, linewidth=3)
        
        ax3.set_title('Explosive Pattern Score (4 = Perfect Storm)', fontweight='bold')
        ax3.set_ylabel('Pattern Score (0-4)')
        ax3.set_ylim(0, 4.5)
        ax3.grid(True, alpha=0.3)
        
        # Chart 4: Signal Distribution
        ax4 = axes[1, 1]
        signal_counts = [
            convergence_df['bearish_signal'].sum(),
            convergence_df['bullish_signal'].sum(), 
            convergence_df['explosive_signal'].sum()
        ]
        signal_labels = ['Bearish', 'Bullish', 'Explosive']
        colors = ['red', 'orange', 'green']
        
        wedges, texts, autotexts = ax4.pie(signal_counts, labels=signal_labels, colors=colors, 
                                          autopct='%1.0f%%', startangle=90)
        ax4.set_title('Distribution of Convergence Signals', fontweight='bold')
        
        # Format x-axes
        for ax in [ax1, ax2, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=7))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.output_dir / "pattern_analysis.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Pattern analysis chart saved to {chart_path}")
        return str(chart_path)
    
    def create_summary_dashboard(self, convergence_df: pd.DataFrame, 
                               summary_stats: Dict) -> str:
        """
        Create summary dashboard with key metrics
        
        Args:
            convergence_df: DataFrame with convergence analysis
            summary_stats: Summary statistics dictionary
            
        Returns:
            Path to saved chart
        """
        fig = plt.figure(figsize=(16, 10))
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        fig.suptitle('SPX Convergence Analysis Dashboard', fontsize=18, fontweight='bold')
        
        # Key metrics boxes
        metrics = [
            ('Total Dates', summary_stats['total_dates_analyzed']),
            ('Bearish Signals', summary_stats['bearish_signals']),
            ('Bullish Signals', summary_stats['bullish_signals']),
            ('Explosive Signals', summary_stats['explosive_signals'])
        ]
        
        for i, (label, value) in enumerate(metrics):
            ax = fig.add_subplot(gs[0, i])
            ax.text(0.5, 0.5, f'{value}', ha='center', va='center', 
                   fontsize=24, fontweight='bold', transform=ax.transAxes)
            ax.text(0.5, 0.2, label, ha='center', va='center', 
                   fontsize=12, transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            
            # Add colored background
            colors = ['lightblue', 'lightcoral', 'lightgreen', 'gold']
            ax.add_patch(plt.Rectangle((0.05, 0.05), 0.9, 0.9, 
                                     facecolor=colors[i], alpha=0.3))
        
        # Time series of signal strength
        ax_ts = fig.add_subplot(gs[1, :])
        dates = pd.to_datetime(convergence_df['date'])
        ax_ts.plot(dates, convergence_df['signal_strength'], 'o-', linewidth=3, markersize=8)
        ax_ts.fill_between(dates, convergence_df['signal_strength'], alpha=0.3)
        ax_ts.set_title('Signal Strength Over Time', fontweight='bold', fontsize=14)
        ax_ts.set_ylabel('Signal Strength')
        ax_ts.grid(True, alpha=0.3)
        ax_ts.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        
        # Greeks correlation matrix
        ax_corr = fig.add_subplot(gs[2, :2])
        greeks_cols = ['charm_scaled', 'vanna_scaled', 'gex_scaled', 'vomma_scaled']
        corr_matrix = convergence_df[greeks_cols].corr()
        
        im = ax_corr.imshow(corr_matrix, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)
        ax_corr.set_xticks(range(len(greeks_cols)))
        ax_corr.set_yticks(range(len(greeks_cols)))
        ax_corr.set_xticklabels(['Charm', 'Vanna', 'GEX', 'Vomma'])
        ax_corr.set_yticklabels(['Charm', 'Vanna', 'GEX', 'Vomma'])
        ax_corr.set_title('Greeks Correlation Matrix', fontweight='bold')
        
        # Add correlation values
        for i in range(len(greeks_cols)):
            for j in range(len(greeks_cols)):
                ax_corr.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}', 
                           ha='center', va='center', fontweight='bold')
        
        # Extreme values summary
        ax_extreme = fig.add_subplot(gs[2, 2:])
        extreme_data = [
            ['Charm', f"{summary_stats['extreme_values']['max_charm']:.1f}", 
             f"{summary_stats['extreme_values']['min_charm']:.1f}"],
            ['Vanna', f"{summary_stats['extreme_values']['max_vanna']:.1f}", 
             f"{summary_stats['extreme_values']['min_vanna']:.1f}"],
            ['GEX', f"{summary_stats['extreme_values']['max_gex']:.1f}", 
             f"{summary_stats['extreme_values']['min_gex']:.1f}"],
            ['Vomma', f"{summary_stats['extreme_values']['max_vomma']:.1f}", 
             f"{summary_stats['extreme_values']['min_vomma']:.1f}"]
        ]
        
        table = ax_extreme.table(cellText=extreme_data,
                               colLabels=['Greek', 'Max', 'Min'],
                               cellLoc='center',
                               loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)
        ax_extreme.axis('off')
        ax_extreme.set_title('Extreme Values Summary', fontweight='bold')
        
        # Save chart
        chart_path = self.output_dir / "summary_dashboard.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Summary dashboard saved to {chart_path}")
        return str(chart_path)

    def _create_forward_convergence_overview(self, convergence_df: pd.DataFrame,
                                           expected_outcomes: Dict[str, Dict]) -> str:
        """
        Create forward-looking convergence overview chart

        Args:
            convergence_df: DataFrame with forward-looking convergence analysis
            expected_outcomes: Expected outcomes from document

        Returns:
            Path to saved chart
        """
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('SPX Forward-Looking Convergence Analysis', fontsize=16, fontweight='bold')

        # Convert date column to datetime
        convergence_df['date'] = pd.to_datetime(convergence_df['date'])
        dates = convergence_df['date']

        # Chart 1: Convergence Pressure Over Time
        ax1 = axes[0, 0]
        ax1.plot(dates, convergence_df['convergence_pressure'], 'o-', color='red',
                linewidth=3, markersize=8, label='Convergence Pressure')
        ax1.set_title('Convergence Pressure Timeline', fontweight='bold')
        ax1.set_ylabel('Convergence Pressure')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Highlight highest pressure date
        max_pressure_idx = convergence_df['convergence_pressure'].idxmax()
        max_pressure_date = convergence_df.loc[max_pressure_idx, 'date']
        max_pressure_value = convergence_df.loc[max_pressure_idx, 'convergence_pressure']
        ax1.annotate(f'Peak: {max_pressure_value:.3f}',
                    xy=(max_pressure_date, max_pressure_value),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        # Chart 2: Signal Strength
        ax2 = axes[0, 1]
        signal_colors = ['red' if x == 1 else 'green' if y == 1 else 'gray'
                        for x, y in zip(convergence_df.get('bearish_signal', [0]*len(dates)),
                                       convergence_df.get('bullish_signal', [0]*len(dates)))]
        ax2.bar(dates, convergence_df.get('signal_strength', [0]*len(dates)),
               color=signal_colors, alpha=0.7)
        ax2.set_title('Signal Strength by Date', fontweight='bold')
        ax2.set_ylabel('Signal Strength')
        ax2.grid(True, alpha=0.3)

        # Chart 3: Days to Target vs Open Interest
        ax3 = axes[1, 0]
        scatter = ax3.scatter(convergence_df['days_to_target'],
                             convergence_df['total_open_interest'],
                             c=convergence_df['convergence_pressure'],
                             cmap='Reds', s=100, alpha=0.7)
        ax3.set_title('Days to Target vs Open Interest', fontweight='bold')
        ax3.set_xlabel('Days to Target')
        ax3.set_ylabel('Total Open Interest')
        ax3.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax3, label='Convergence Pressure')

        # Chart 4: Charm Exposure (Weekend Decay)
        ax4 = axes[1, 1]
        if 'charm_exposure' in convergence_df.columns:
            # Plot charm exposure over time
            ax4.plot(dates, convergence_df['charm_exposure'], 'o-', color='purple',
                    linewidth=2, markersize=6, label='Charm Exposure')

            # Highlight weekend effect (higher charm for near-term dates)
            weekend_dates = convergence_df[convergence_df['days_to_target'] <= 7]
            if not weekend_dates.empty:
                ax4.scatter(weekend_dates['date'], weekend_dates['charm_exposure'],
                           color='orange', s=80, marker='s', label='Weekend Effect', zorder=5)

            # Annotate highest charm exposure
            max_charm_idx = convergence_df['charm_exposure'].idxmax()
            max_charm_date = convergence_df.loc[max_charm_idx, 'date']
            max_charm_value = convergence_df.loc[max_charm_idx, 'charm_exposure']
            ax4.annotate(f'Peak Charm: {max_charm_value:.4f}',
                        xy=(max_charm_date, max_charm_value),
                        xytext=(10, 10), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7),
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

            ax4.set_title('Charm Exposure (Weekend Decay)', fontweight='bold')
            ax4.set_ylabel('Charm Exposure')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        else:
            # Fallback to analysis type distribution if no charm data
            analysis_types = convergence_df['expected_analysis_type'].value_counts()
            colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
            wedges, texts, autotexts = ax4.pie(analysis_types.values,
                                              labels=analysis_types.index,
                                              autopct='%1.1f%%',
                                              colors=colors[:len(analysis_types)])
            ax4.set_title('Analysis Type Distribution', fontweight='bold')

        plt.tight_layout()

        # Save chart
        chart_path = self.output_dir / f"forward_convergence_overview_{self.ticker.lower()}.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Forward convergence overview chart saved to {chart_path}")
        return str(chart_path)

    def _create_forward_heatmap(self, convergence_df: pd.DataFrame) -> str:
        """
        Create heatmap for forward-looking convergence data

        Args:
            convergence_df: DataFrame with forward-looking convergence analysis

        Returns:
            Path to saved chart
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle('Forward-Looking Convergence Heatmap', fontsize=16, fontweight='bold')

        # Prepare data for heatmap
        heatmap_data = convergence_df[['date', 'convergence_pressure', 'signal_strength',
                                     'total_open_interest', 'days_to_target']].copy()

        heatmap_data['date'] = pd.to_datetime(heatmap_data['date']).dt.strftime('%m-%d')
        heatmap_data = heatmap_data.set_index('date')

        # Normalize data for better visualization
        normalized_data = heatmap_data.copy()
        for col in normalized_data.columns:
            normalized_data[col] = (normalized_data[col] - normalized_data[col].min()) / \
                                  (normalized_data[col].max() - normalized_data[col].min())

        # Heatmap 1: Normalized metrics
        im1 = ax1.imshow(normalized_data.T, cmap='RdYlBu_r', aspect='auto')
        ax1.set_title('Normalized Convergence Metrics', fontweight='bold')
        ax1.set_xticks(range(len(normalized_data.index)))
        ax1.set_xticklabels(normalized_data.index, rotation=45)
        ax1.set_yticks(range(len(normalized_data.columns)))
        ax1.set_yticklabels(normalized_data.columns)

        # Add colorbar
        cbar1 = plt.colorbar(im1, ax=ax1)
        cbar1.set_label('Normalized Value (0-1)')

        # Heatmap 2: Raw convergence pressure
        pressure_matrix = convergence_df.pivot_table(
            index='expected_analysis_type',
            columns='days_to_target',
            values='convergence_pressure',
            aggfunc='mean'
        )

        im2 = ax2.imshow(pressure_matrix.values, cmap='Reds', aspect='auto')
        ax2.set_title('Convergence Pressure by Type & Days', fontweight='bold')
        ax2.set_xticks(range(len(pressure_matrix.columns)))
        ax2.set_xticklabels(pressure_matrix.columns)
        ax2.set_yticks(range(len(pressure_matrix.index)))
        ax2.set_yticklabels(pressure_matrix.index, rotation=0)

        # Add colorbar
        cbar2 = plt.colorbar(im2, ax=ax2)
        cbar2.set_label('Convergence Pressure')

        plt.tight_layout()

        # Save chart
        chart_path = self.output_dir / f"forward_heatmap_{self.ticker.lower()}.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Forward heatmap saved to {chart_path}")
        return str(chart_path)

    def _create_forward_pattern_analysis(self, convergence_df: pd.DataFrame) -> str:
        """
        Create pattern analysis for forward-looking convergence data

        Args:
            convergence_df: DataFrame with forward-looking convergence analysis

        Returns:
            Path to saved chart
        """
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Forward-Looking Pattern Analysis', fontsize=16, fontweight='bold')

        # Convert date column to datetime
        convergence_df['date'] = pd.to_datetime(convergence_df['date'])
        dates = convergence_df['date']

        # Chart 1: Convergence Pressure vs Days to Target
        ax1 = axes[0, 0]
        scatter = ax1.scatter(convergence_df['days_to_target'],
                             convergence_df['convergence_pressure'],
                             c=convergence_df.get('signal_strength', [0]*len(convergence_df)),
                             cmap='viridis', s=100, alpha=0.7)
        ax1.set_title('Convergence Pressure vs Days to Target', fontweight='bold')
        ax1.set_xlabel('Days to Target')
        ax1.set_ylabel('Convergence Pressure')
        ax1.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax1, label='Gamma Exposure')

        # Chart 2: Open Interest Distribution by Analysis Type
        ax2 = axes[0, 1]
        analysis_types = convergence_df['expected_analysis_type'].unique()
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']

        for i, analysis_type in enumerate(analysis_types):
            subset = convergence_df[convergence_df['expected_analysis_type'] == analysis_type]
            ax2.bar(subset['date'], subset['total_open_interest'],
                   alpha=0.7, label=analysis_type, color=colors[i % len(colors)])

        ax2.set_title('Open Interest by Analysis Type', fontweight='bold')
        ax2.set_ylabel('Total Open Interest')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Chart 3: Signal Timeline
        ax3 = axes[1, 0]

        # Plot convergence pressure line
        ax3.plot(dates, convergence_df['convergence_pressure'], 'o-',
                color='blue', linewidth=2, markersize=6, label='Convergence Pressure')

        # Highlight bullish signals
        if 'bullish_signal' in convergence_df.columns:
            bullish_dates = convergence_df[convergence_df['bullish_signal'] == 1]
            if not bullish_dates.empty:
                ax3.scatter([pd.to_datetime(d) for d in bullish_dates['date']],
                           bullish_dates['convergence_pressure'],
                           color='green', s=100, marker='^', label='Bullish Signal', zorder=5)

        # Highlight bearish signals
        if 'bearish_signal' in convergence_df.columns:
            bearish_dates = convergence_df[convergence_df['bearish_signal'] == 1]
            if not bearish_dates.empty:
                ax3.scatter([pd.to_datetime(d) for d in bearish_dates['date']],
                           bearish_dates['convergence_pressure'],
                           color='red', s=100, marker='v', label='Bearish Signal', zorder=5)

        ax3.set_title('Convergence Signals Timeline', fontweight='bold')
        ax3.set_ylabel('Convergence Pressure')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Chart 4: Risk Distribution
        ax4 = axes[1, 1]

        # Create risk categories based on convergence pressure
        risk_categories = []
        for pressure in convergence_df['convergence_pressure']:
            if pressure > 0.85:
                risk_categories.append('High Risk')
            elif pressure > 0.75:
                risk_categories.append('Medium Risk')
            else:
                risk_categories.append('Low Risk')

        risk_counts = pd.Series(risk_categories).value_counts()
        colors_risk = ['red', 'orange', 'green']
        ax4.pie(risk_counts.values, labels=risk_counts.index, autopct='%1.1f%%',
               colors=colors_risk[:len(risk_counts)])
        ax4.set_title('Risk Distribution', fontweight='bold')

        plt.tight_layout()

        # Save chart
        chart_path = self.output_dir / f"forward_pattern_analysis_{self.ticker.lower()}.png"
        plt.savefig(chart_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        logger.info(f"Forward pattern analysis saved to {chart_path}")
        return str(chart_path)
    
    def save_all_charts(self, convergence_df: pd.DataFrame, 
                       expected_outcomes: Dict, summary_stats: Dict) -> List[str]:
        """
        Generate and save all charts
        
        Args:
            convergence_df: DataFrame with convergence analysis
            expected_outcomes: Expected outcomes from document
            summary_stats: Summary statistics
            
        Returns:
            List of paths to saved charts
        """
        chart_paths = []
        
        try:
            chart_paths.append(self.create_convergence_overview_chart(convergence_df, expected_outcomes))
            chart_paths.append(self.create_greeks_heatmap(convergence_df))
            chart_paths.append(self.create_pattern_analysis_chart(convergence_df))
            chart_paths.append(self.create_summary_dashboard(convergence_df, summary_stats))
            
            logger.info(f"All charts saved successfully. Total: {len(chart_paths)}")
            
        except Exception as e:
            logger.error(f"Error creating charts: {e}")
            
        return chart_paths

