"""
Main Analysis Script for Options Convergence Analysis
Orchestrates the entire analysis pipeline for any ticker (SPX, VIX, TLT, etc.)
"""
import sys
import shutil
import os
from pathlib import Path
import pandas as pd
import numpy as np
import logging
from typing import Dict

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent / "config"))

# Import our modules
from data_loader import OptionsDataLoader
from analytics_engine import GreeksCalculator, PortfolioGreeksCalculator
from convergence_processor import ConvergenceAnalyzer
from advanced_convergence_analyzer import AdvancedConvergenceAnalyzer
from narrative_generator import ConvergenceNarrativeGenerator
from visualization import ConvergenceVisualizer
from report_generator import PDFReportGenerator

# Import configuration
from config import (
    DATA_FILE, TICKER, REPORTS_DIR, CONVERGENCE_DATES, EXPECTED_OUTCOMES,
    RISK_FREE_RATE, DIVIDEND_YIELD, OPENAI_API_KEY
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptionsConvergenceAnalysis:
    """
    Main class orchestrating the options convergence analysis for any ticker
    """

    def __init__(self, ticker=None):
        """Initialize the analysis pipeline"""
        # Use provided ticker or default from config
        self.ticker = ticker or TICKER

        # Clean up at startup
        self._cleanup_at_startup()

        self.data_loader = None
        self.analyzer = None
        self.advanced_analyzer = None
        self.narrative_generator = None
        self.visualizer = None
        self.report_generator = None
        self.results = {}
        
    def setup_pipeline(self):
        """Set up the analysis pipeline components"""
        logger.info("Setting up analysis pipeline...")

        # Initialize data loader
        self.data_loader = OptionsDataLoader(DATA_FILE, self.ticker)

        # Initialize Greeks calculator with config values
        greeks_calc = GreeksCalculator(
            risk_free_rate=RISK_FREE_RATE,
            dividend_yield=DIVIDEND_YIELD
        )

        # Initialize analyzers with configured Greeks calculator
        self.analyzer = ConvergenceAnalyzer(self.data_loader, greeks_calc)
        self.advanced_analyzer = AdvancedConvergenceAnalyzer(self.data_loader, greeks_calc)

        # Initialize narrative generator with OpenAI
        self.narrative_generator = ConvergenceNarrativeGenerator(OPENAI_API_KEY, self.ticker)

        # Initialize visualizer
        self.visualizer = ConvergenceVisualizer(REPORTS_DIR, self.ticker)

        # Initialize report generator
        self.report_generator = PDFReportGenerator(REPORTS_DIR)

        logger.info("Pipeline setup complete")

    def _cleanup_at_startup(self):
        """Clean up data directory and reports at startup"""
        logger.info("Performing startup cleanup...")

        try:
            # Clean reports directory
            if REPORTS_DIR.exists():
                logger.info(f"Cleaning reports directory: {REPORTS_DIR}")
                for item in REPORTS_DIR.iterdir():
                    if item.is_file():
                        item.unlink()
                        logger.debug(f"Removed file: {item}")
                    elif item.is_dir():
                        shutil.rmtree(item)
                        logger.debug(f"Removed directory: {item}")
            else:
                REPORTS_DIR.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created reports directory: {REPORTS_DIR}")

            # Remove data directory if it exists (will be recreated by data processing)
            data_dir = Path(__file__).parent.parent / "data"
            if data_dir.exists():
                logger.info(f"Removing data directory: {data_dir}")
                shutil.rmtree(data_dir)
                logger.info("Data directory removed")

            logger.info("Startup cleanup completed successfully")

        except Exception as e:
            logger.warning(f"Error during startup cleanup: {e}")

    def load_and_process_data(self):
        """Load and process the options data"""
        logger.info("Loading and processing data...")
        
        # Load and clean data
        self.data_loader.load_raw_data()
        self.data_loader.clean_data()
        
        # Get data summary
        summary_stats = self.data_loader.get_summary_stats()
        logger.info(f"Data summary: {summary_stats}")
        
        self.results['data_summary'] = summary_stats
        
    def run_convergence_analysis(self):
        """Run rigorous convergence analysis for all target dates"""
        logger.info("Running advanced convergence analysis...")

        convergence_results = []

        for date in CONVERGENCE_DATES:
            logger.info(f"Analyzing convergence for {date}")

            # Get expected analysis type for this date
            expected_outcome = EXPECTED_OUTCOMES.get(date, {})
            analysis_type = expected_outcome.get('direction', 'ANALYSIS_REQUIRED')
            primary_greek = expected_outcome.get('primary_greek', 'delta')
            is_expiration = expected_outcome.get('is_expiration_date', False)

            # For forward-looking analysis, always use the new forward convergence method
            if is_expiration or any(keyword in analysis_type for keyword in ['GAMMA_PIN', 'THETA_DECAY', 'VEGA_SENSITIVITY', 'DELTA_HEDGING']):
                # This is a forward-looking analysis target
                advanced_metrics = self._run_comprehensive_analysis(date)
                # Create a basic metrics placeholder since we don't have historical data for future dates
                basic_metrics = {
                    'date': date,
                    'analysis_type': 'FORWARD_LOOKING',
                    'total_contracts': expected_outcome.get('contract_count', 0),
                    'total_open_interest': expected_outcome.get('total_open_interest', 0),
                    'gamma_exposure': expected_outcome.get('gamma_exposure', 0)
                }
            else:
                # Traditional historical analysis
                if 'GAMMA' in analysis_type or primary_greek == 'gamma':
                    advanced_metrics = self.advanced_analyzer.analyze_gamma_convergence(date)
                elif 'THETA' in analysis_type or primary_greek == 'theta':
                    advanced_metrics = self.advanced_analyzer.analyze_theta_decay_convergence(date)
                elif 'VEGA' in analysis_type or primary_greek == 'vega':
                    advanced_metrics = self.advanced_analyzer.analyze_vega_convergence(date)
                else:
                    advanced_metrics = self._run_comprehensive_analysis(date)

                # Also run basic analysis for comparison
                basic_metrics = self.analyzer.calculate_daily_metrics(date)

            if advanced_metrics and basic_metrics:
                # Combine advanced and basic metrics
                combined_metrics = {
                    **advanced_metrics,
                    **basic_metrics,
                    'date': date,
                    'expected_analysis_type': analysis_type,
                    'expected_primary_greek': primary_greek
                }
                convergence_results.append(combined_metrics)
                logger.info(f"Completed rigorous analysis for {date} - Type: {analysis_type}")
            else:
                logger.warning(f"Insufficient data for rigorous analysis on {date}")

        # Store results
        convergence_data = pd.DataFrame(convergence_results)

        if not convergence_data.empty:
            # Save detailed results to CSV
            output_file = REPORTS_DIR / "advanced_convergence_data.csv"
            convergence_data.to_csv(output_file, index=False)
            logger.info(f"Advanced convergence data saved to {output_file}")

            # Generate summary statistics
            self._generate_analysis_summary(convergence_data)

            # Generate AI-powered narratives
            self._generate_convergence_narratives(convergence_data)

            # Store in results for compatibility
            self.results['convergence_data'] = convergence_data
            self.results['signals_data'] = convergence_data  # For compatibility
            self.results['summary_report'] = self._create_summary_report(convergence_data)

        logger.info("Advanced convergence analysis complete")
        return not convergence_data.empty

    def _run_comprehensive_analysis(self, date: str) -> Dict:
        """Run FORWARD-LOOKING comprehensive analysis for convergence prediction"""
        logger.info(f"Running comprehensive analysis for {date}")

        # Use the new forward-looking analysis method
        return self.advanced_analyzer.analyze_forward_convergence(date)

    def _generate_analysis_summary(self, convergence_data: pd.DataFrame):
        """Generate summary statistics for the analysis"""
        if convergence_data.empty:
            return

        logger.info("Generating analysis summary...")

        # Analysis type distribution
        analysis_types = convergence_data['analysis_type'].value_counts()
        logger.info(f"Analysis types: {dict(analysis_types)}")

        # Key metrics summary
        numeric_columns = convergence_data.select_dtypes(include=[np.number]).columns
        summary_stats = convergence_data[numeric_columns].describe()

        # Save summary
        summary_file = REPORTS_DIR / "analysis_summary.csv"
        summary_stats.to_csv(summary_file)
        logger.info(f"Analysis summary saved to {summary_file}")

        # Log key insights
        if 'total_gamma_exposure' in convergence_data.columns:
            max_gamma_date = convergence_data.loc[
                convergence_data['total_gamma_exposure'].idxmax(), 'date'
            ]
            logger.info(f"Highest gamma exposure on: {max_gamma_date}")

        if 'convergence_pressure' in convergence_data.columns:
            max_pressure_date = convergence_data.loc[
                convergence_data['convergence_pressure'].idxmax(), 'date'
            ]
            logger.info(f"Highest convergence pressure on: {max_pressure_date}")

    def _create_summary_report(self, convergence_data: pd.DataFrame) -> Dict:
        """Create summary report for compatibility"""
        if convergence_data.empty:
            return {}

        # Calculate signal counts
        bearish_signals = convergence_data.get('bearish_signal', pd.Series(0)).sum()
        bullish_signals = convergence_data.get('bullish_signal', pd.Series(0)).sum()
        explosive_signals = convergence_data.get('explosive_signal', pd.Series(0)).sum()

        # Calculate average metrics for scaled columns
        average_metrics = {}
        for col in ['charm_scaled', 'vanna_scaled', 'gex_scaled', 'vomma_scaled', 'volm_bs_scaled', 'vx_oi_scaled']:
            if col in convergence_data.columns:
                average_metrics[col.replace('_scaled', '')] = convergence_data[col].mean()

        return {
            'total_dates_analyzed': len(convergence_data),
            'bearish_signals': int(bearish_signals),
            'bullish_signals': int(bullish_signals),
            'explosive_signals': int(explosive_signals),
            'analysis_types': convergence_data['analysis_type'].value_counts().to_dict(),
            'avg_contracts_per_date': convergence_data.get('total_contracts', pd.Series()).mean(),
            'date_range': f"{convergence_data['date'].min()} to {convergence_data['date'].max()}",
            'average_metrics': average_metrics
        }

    def _generate_convergence_narratives(self, convergence_data: pd.DataFrame):
        """Generate AI-powered convergence analysis narratives"""
        if convergence_data.empty:
            return

        logger.info("Generating AI-powered convergence narratives...")

        try:
            # Generate comprehensive narrative
            summary_stats = self.results.get('data_summary', {})
            comprehensive_narrative = self.narrative_generator.generate_convergence_narrative(
                convergence_data, summary_stats
            )

            # Generate executive summary
            executive_summary = self.narrative_generator.generate_executive_summary(convergence_data)

            # Generate date-specific narratives for key dates
            date_narratives = {}
            # Use available column for selecting key dates
            sort_column = 'total_contracts' if 'total_contracts' in convergence_data.columns else 'convergence_pressure'
            if sort_column in convergence_data.columns:
                key_dates = convergence_data.nlargest(5, sort_column)['date'].tolist()
            else:
                key_dates = convergence_data['date'].head(5).tolist()

            for date in key_dates:
                date_data = convergence_data[convergence_data['date'] == date].iloc[0].to_dict()
                narrative = self.narrative_generator.generate_date_specific_narrative(date, date_data)
                date_narratives[str(date)] = narrative

            # Save narratives to files
            narratives_dir = REPORTS_DIR / "narratives"
            narratives_dir.mkdir(exist_ok=True)

            # Save comprehensive narrative
            with open(narratives_dir / "comprehensive_analysis.txt", 'w') as f:
                f.write(f"{self.ticker} CONVERGENCE ANALYSIS - COMPREHENSIVE NARRATIVE\n")
                f.write("=" * 60 + "\n\n")
                f.write(comprehensive_narrative)

            # Save executive summary
            with open(narratives_dir / "executive_summary.txt", 'w') as f:
                f.write(f"{self.ticker} CONVERGENCE ANALYSIS - EXECUTIVE SUMMARY\n")
                f.write("=" * 50 + "\n\n")
                f.write(executive_summary)

            # Save date-specific narratives
            with open(narratives_dir / "date_specific_insights.txt", 'w') as f:
                f.write(f"{self.ticker} CONVERGENCE ANALYSIS - DATE-SPECIFIC INSIGHTS\n")
                f.write("=" * 55 + "\n\n")
                for date, narrative in date_narratives.items():
                    f.write(f"Analysis for {date}:\n")
                    f.write("-" * 30 + "\n")
                    f.write(narrative)
                    f.write("\n\n")

            # Store in results
            self.results['narratives'] = {
                'comprehensive': comprehensive_narrative,
                'executive_summary': executive_summary,
                'date_specific': date_narratives
            }

            logger.info(f"Generated narratives saved to {narratives_dir}")

        except Exception as e:
            logger.error(f"Error generating narratives: {e}")
            # Create fallback narrative
            fallback_narrative = f"""
            {self.ticker} Convergence Analysis completed for {len(convergence_data)} dates
            from {convergence_data['date'].min()} to {convergence_data['date'].max()}.

            Analysis identified convergence patterns across multiple time horizons with
            comprehensive Greeks analysis and risk assessment.

            Note: AI narrative generation encountered an issue. Please refer to the
            quantitative results in the CSV files for detailed analysis.
            """

            self.results['narratives'] = {
                'comprehensive': fallback_narrative,
                'executive_summary': "Analysis completed successfully with quantitative results available.",
                'date_specific': {}
            }

    def generate_visualizations(self):
        """Generate all visualizations"""
        logger.info("Generating visualizations...")
        
        try:
            chart_paths = self.visualizer.save_all_charts(
                self.results['signals_data'],
                EXPECTED_OUTCOMES,
                self.results['summary_report']
            )
            
            self.results['chart_paths'] = chart_paths
            logger.info(f"Generated {len(chart_paths)} charts")
            
        except Exception as e:
            logger.error(f"Error generating visualizations: {e}")
            self.results['chart_paths'] = []
    
    def generate_pdf_report(self):
        """Generate PDF report"""
        logger.info("Generating PDF report...")
        
        try:
            report_path = self.report_generator.create_full_report(
                convergence_data=self.results['signals_data'],
                summary_stats=self.results['summary_report'],
                chart_paths=self.results['chart_paths'],
                expected_outcomes=EXPECTED_OUTCOMES
            )
            
            self.results['report_path'] = report_path
            logger.info(f"PDF report generated: {report_path}")
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {e}")
            self.results['report_path'] = None
    
    def save_data_exports(self):
        """Save data exports to CSV files"""
        logger.info("Saving data exports...")
        
        try:
            # Save convergence data
            convergence_path = REPORTS_DIR / "convergence_data.csv"
            self.results['signals_data'].to_csv(convergence_path, index=False)
            
            # Save summary statistics
            summary_path = REPORTS_DIR / "summary_statistics.csv"
            summary_df = pd.DataFrame([self.results['summary_report']['average_metrics']])
            summary_df.to_csv(summary_path, index=False)
            
            self.results['data_exports'] = [str(convergence_path), str(summary_path)]
            logger.info("Data exports saved")
            
        except Exception as e:
            logger.error(f"Error saving data exports: {e}")
            self.results['data_exports'] = []
    
    def run_full_analysis(self):
        """Run the complete analysis pipeline"""
        logger.info(f"Starting {self.ticker} Convergence Analysis...")
        
        try:
            # Setup pipeline
            self.setup_pipeline()
            
            # Load and process data
            self.load_and_process_data()
            
            # Run convergence analysis
            if not self.run_convergence_analysis():
                logger.error("Convergence analysis failed")
                return False
            
            # Generate visualizations
            self.generate_visualizations()
            
            # Generate PDF report
            self.generate_pdf_report()
            
            # Save data exports
            self.save_data_exports()
            
            logger.info("Analysis pipeline completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Analysis pipeline failed: {e}")
            return False
    
    def get_results_summary(self):
        """Get summary of analysis results"""
        if not self.results:
            return "No analysis results available"
        
        summary = f"""
SPX Convergence Analysis Results Summary
========================================

Data Summary:
- Total rows processed: {self.results.get('data_summary', {}).get('total_rows', 'N/A')}
- Date range: {self.results.get('data_summary', {}).get('date_range', 'N/A')}
- Unique dates: {self.results.get('data_summary', {}).get('unique_dates', 'N/A')}

Convergence Analysis:
- Total convergence dates analyzed: {self.results.get('summary_report', {}).get('total_dates_analyzed', 'N/A')}
- Bearish signals detected: {self.results.get('summary_report', {}).get('bearish_signals', 'N/A')}
- Bullish signals detected: {self.results.get('summary_report', {}).get('bullish_signals', 'N/A')}
- Explosive signals detected: {self.results.get('summary_report', {}).get('explosive_signals', 'N/A')}

Generated Files:
- Charts: {len(self.results.get('chart_paths', []))} files
- PDF Report: {'Yes' if self.results.get('report_path') else 'No'}
- Data Exports: {len(self.results.get('data_exports', []))} files

Output Directory: {REPORTS_DIR}
        """
        
        return summary

def main():
    """Main function to run the analysis"""
    # Create analysis instance
    analysis = OptionsConvergenceAnalysis()
    
    # Run full analysis
    success = analysis.run_full_analysis()
    
    if success:
        print("Analysis completed successfully!")
        print(analysis.get_results_summary())
    else:
        print("Analysis failed. Check logs for details.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

