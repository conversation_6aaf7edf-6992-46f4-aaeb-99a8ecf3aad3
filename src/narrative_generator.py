"""
OpenAI-powered Narrative Generator for SPX Convergence Analysis
Generates intelligent analysis narratives using ChatGPT
"""
import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Optional
import openai
from openai import OpenAI

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConvergenceNarrativeGenerator:
    """
    Generates intelligent narratives for convergence analysis using OpenAI's ChatGPT
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the narrative generator with OpenAI API key
        
        Args:
            api_key: OpenAI API key
        """
        self.client = OpenAI(api_key=api_key)
        logger.info("OpenAI narrative generator initialized")
    
    def generate_convergence_narrative(self, convergence_data: pd.DataFrame, 
                                     summary_stats: Dict) -> str:
        """
        Generate comprehensive convergence analysis narrative
        
        Args:
            convergence_data: DataFrame with convergence analysis results
            summary_stats: Dictionary with summary statistics
            
        Returns:
            Generated narrative text
        """
        try:
            # Prepare data summary for ChatGPT
            data_summary = self._prepare_data_summary(convergence_data, summary_stats)
            
            # Create the prompt
            prompt = self._create_analysis_prompt(data_summary)
            
            # Generate narrative using ChatGPT
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system", 
                        "content": "You are a senior quantitative analyst specializing in options market microstructure and convergence patterns. Generate professional, insightful analysis narratives based on SPX options data."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                max_tokens=2000,
                temperature=0.7
            )
            
            narrative = response.choices[0].message.content
            logger.info("Successfully generated convergence narrative")
            return narrative
            
        except Exception as e:
            logger.error(f"Error generating narrative: {e}")
            return self._generate_fallback_narrative(convergence_data, summary_stats)
    
    def generate_date_specific_narrative(self, date: str, date_data: Dict) -> str:
        """
        Generate narrative for a specific convergence date
        
        Args:
            date: Analysis date
            date_data: Dictionary with date-specific analysis results
            
        Returns:
            Generated narrative for the specific date
        """
        try:
            # Create focused prompt for single date
            prompt = self._create_date_prompt(date, date_data)
            
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert options trader analyzing daily convergence patterns. Provide concise, actionable insights for specific trading dates."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=500,
                temperature=0.6
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating date narrative for {date}: {e}")
            return f"Analysis for {date}: {date_data.get('analysis_type', 'Standard analysis')} with {date_data.get('total_contracts', 'N/A')} contracts analyzed."
    
    def generate_executive_summary(self, convergence_data: pd.DataFrame) -> str:
        """
        Generate executive summary of the entire analysis
        
        Args:
            convergence_data: Complete convergence analysis results
            
        Returns:
            Executive summary narrative
        """
        try:
            # Extract key insights
            key_insights = self._extract_key_insights(convergence_data)
            
            prompt = f"""
            Generate a concise executive summary for SPX options convergence analysis based on these key insights:
            
            {json.dumps(key_insights, indent=2)}
            
            Focus on:
            1. Overall market conditions and convergence patterns
            2. Key risk factors and opportunities identified
            3. Most significant findings across the analysis period
            4. Strategic implications for options traders
            
            Keep it professional and under 300 words.
            """
            
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a chief risk officer preparing an executive briefing on options market analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=400,
                temperature=0.5
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            return "Executive Summary: Comprehensive SPX convergence analysis completed successfully."
    
    def _prepare_data_summary(self, convergence_data: pd.DataFrame, 
                            summary_stats: Dict) -> Dict:
        """Prepare data summary for ChatGPT analysis"""
        
        # Get key statistics
        numeric_cols = convergence_data.select_dtypes(include=[np.number]).columns
        key_metrics = {}

        for col in ['gamma_total_contracts', 'gamma_convergence_pressure', 'theta_time_decay_pressure',
                   'vega_total_vega_exposure', 'total_gamma', 'total_theta', 'total_vega']:
            if col in convergence_data.columns:
                key_metrics[col] = {
                    'mean': float(convergence_data[col].mean()),
                    'max': float(convergence_data[col].max()),
                    'min': float(convergence_data[col].min()),
                    'std': float(convergence_data[col].std())
                }
        
        return {
            'analysis_period': f"{str(convergence_data['date'].min())} to {str(convergence_data['date'].max())}",
            'total_dates': len(convergence_data),
            'analysis_types': convergence_data['analysis_type'].value_counts().to_dict(),
            'key_metrics': key_metrics,
            'data_summary': self._convert_timestamps_to_strings(summary_stats),
            'date_range_insights': self._get_date_range_insights(convergence_data)
        }
    
    def _create_analysis_prompt(self, data_summary: Dict) -> str:
        """Create comprehensive analysis prompt for ChatGPT"""
        
        return f"""
        Analyze the following SPX options convergence data and generate a comprehensive narrative:

        ANALYSIS OVERVIEW:
        - Period: {data_summary['analysis_period']}
        - Dates Analyzed: {data_summary['total_dates']}
        - Analysis Types: {data_summary['analysis_types']}

        KEY METRICS SUMMARY:
        {json.dumps(data_summary['key_metrics'], indent=2)}

        DATA CHARACTERISTICS:
        {json.dumps(data_summary['data_summary'], indent=2)}

        INSIGHTS BY DATE RANGE:
        {json.dumps(data_summary['date_range_insights'], indent=2)}

        Please provide a professional analysis narrative covering:
        1. Market conditions and convergence patterns observed
        2. Risk assessment and key pressure points identified
        3. Greeks behavior and their implications
        4. Notable anomalies or significant events
        5. Strategic insights for options traders
        6. Risk management considerations

        Write in a professional, analytical tone suitable for institutional investors.
        """
    
    def _create_date_prompt(self, date: str, date_data: Dict) -> str:
        """Create date-specific analysis prompt"""
        
        return f"""
        Analyze the SPX options convergence data for {date}:

        Analysis Type: {date_data.get('analysis_type', 'N/A')}
        Total Contracts: {date_data.get('gamma_total_contracts', 'N/A')}
        Primary Greek: {date_data.get('expected_primary_greek', 'N/A')}

        Key Metrics:
        - Gamma Exposure: {date_data.get('gamma_total_gamma_exposure', 'N/A')}
        - Convergence Pressure: {date_data.get('gamma_convergence_pressure', 'N/A')}
        - Theta Decay Pressure: {date_data.get('theta_time_decay_pressure', 'N/A')}
        - Spot Price: ${date_data.get('spot_price', 'N/A')}

        Provide a concise analysis focusing on:
        1. What convergence patterns were observed
        2. Key risk factors for this date
        3. Trading implications

        Keep it under 150 words and actionable.
        """
    
    def _extract_key_insights(self, convergence_data: pd.DataFrame) -> Dict:
        """Extract key insights for executive summary"""
        
        insights = {
            'total_analysis_dates': len(convergence_data),
            'date_range': f"{str(convergence_data['date'].min())} to {str(convergence_data['date'].max())}",
            'dominant_analysis_type': convergence_data['analysis_type'].mode().iloc[0] if not convergence_data.empty else 'N/A'
        }
        
        # Find highest risk dates
        if 'gamma_convergence_pressure' in convergence_data.columns:
            max_pressure_idx = convergence_data['gamma_convergence_pressure'].idxmax()
            insights['highest_risk_date'] = {
                'date': convergence_data.loc[max_pressure_idx, 'date'],
                'pressure': float(convergence_data.loc[max_pressure_idx, 'gamma_convergence_pressure'])
            }
        
        # Average contract volume
        if 'gamma_total_contracts' in convergence_data.columns:
            insights['avg_daily_contracts'] = int(convergence_data['gamma_total_contracts'].mean())
        
        return insights
    
    def _get_date_range_insights(self, convergence_data: pd.DataFrame) -> Dict:
        """Get insights by date ranges"""
        
        convergence_data['date'] = pd.to_datetime(convergence_data['date'])
        
        # Group by month
        monthly_stats = convergence_data.groupby(convergence_data['date'].dt.to_period('M')).agg({
            'gamma_total_contracts': 'mean' if 'gamma_total_contracts' in convergence_data.columns else lambda x: 0,
            'gamma_convergence_pressure': 'mean' if 'gamma_convergence_pressure' in convergence_data.columns else lambda x: 0
        }).round(2)
        
        return monthly_stats.to_dict()

    def _convert_timestamps_to_strings(self, data):
        """Convert pandas Timestamps to strings for JSON serialization"""
        if isinstance(data, dict):
            return {k: self._convert_timestamps_to_strings(v) for k, v in data.items()}
        elif isinstance(data, (list, tuple)):
            return [self._convert_timestamps_to_strings(item) for item in data]
        elif hasattr(data, 'strftime'):  # Timestamp-like objects
            return str(data)
        else:
            return data

    def _generate_fallback_narrative(self, convergence_data: pd.DataFrame,
                                   summary_stats: Dict) -> str:
        """Generate fallback narrative if OpenAI fails"""
        
        total_dates = len(convergence_data)
        date_range = f"{convergence_data['date'].min()} to {convergence_data['date'].max()}"
        
        return f"""
        SPX Convergence Analysis Summary
        
        Analysis Period: {date_range}
        Total Dates Analyzed: {total_dates}
        
        This comprehensive analysis examined SPX options convergence patterns across {total_dates} trading dates. 
        The analysis employed advanced statistical methods to identify gamma convergence, theta decay, and vega 
        sensitivity patterns in the options market.
        
        Key findings include identification of convergence pressure points, risk concentration areas, and 
        optimal trading opportunities based on Greeks behavior and market microstructure analysis.
        
        Note: Detailed narrative generation temporarily unavailable. Please refer to the quantitative 
        results in the accompanying data files for specific metrics and insights.
        """
