"""
OpenAI-powered Narrative Generator for SPX Convergence Analysis
Generates intelligent analysis narratives using ChatGPT
"""
import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Optional
import openai
from openai import OpenAI

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConvergenceNarrativeGenerator:
    """
    Generates intelligent narratives for convergence analysis using OpenAI's ChatGPT
    """

    def __init__(self, api_key: str, ticker: str = "SPX"):
        """
        Initialize the narrative generator with OpenAI API key

        Args:
            api_key: OpenAI API key
            ticker: The ticker symbol being analyzed
        """
        self.client = OpenAI(api_key=api_key)
        self.ticker = ticker.upper()
        logger.info(f"OpenAI narrative generator initialized for {self.ticker}")
    
    def generate_convergence_narrative(self, convergence_data: pd.DataFrame, 
                                     summary_stats: Dict) -> str:
        """
        Generate comprehensive convergence analysis narrative
        
        Args:
            convergence_data: DataFrame with convergence analysis results
            summary_stats: Dictionary with summary statistics
            
        Returns:
            Generated narrative text
        """
        try:
            # Prepare data summary for ChatGPT
            data_summary = self._prepare_data_summary(convergence_data, summary_stats)
            
            # Create the prompt
            prompt = self._create_analysis_prompt(data_summary)
            
            # Generate narrative using ChatGPT
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": f"You are a senior quantitative analyst specializing in options market microstructure and convergence patterns. Generate professional, insightful analysis narratives based on {self.ticker} options data."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                max_tokens=2000,
                temperature=0.7
            )
            
            narrative = response.choices[0].message.content
            logger.info("Successfully generated convergence narrative")
            return narrative
            
        except Exception as e:
            logger.error(f"Error generating narrative: {e}")
            return self._generate_fallback_narrative(convergence_data, summary_stats)
    
    def generate_date_specific_narrative(self, date: str, date_data: Dict) -> str:
        """
        Generate narrative for a specific convergence date
        
        Args:
            date: Analysis date
            date_data: Dictionary with date-specific analysis results
            
        Returns:
            Generated narrative for the specific date
        """
        try:
            # Create focused prompt for single date
            prompt = self._create_date_prompt(date, date_data)
            
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert options trader analyzing daily convergence patterns. Provide concise, actionable insights for specific trading dates."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=500,
                temperature=0.6
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating date narrative for {date}: {e}")
            return f"Analysis for {date}: {date_data.get('analysis_type', 'Standard analysis')} with {date_data.get('total_contracts', 'N/A')} contracts analyzed."
    
    def generate_executive_summary(self, convergence_data: pd.DataFrame) -> str:
        """
        Generate executive summary of the entire analysis
        
        Args:
            convergence_data: Complete convergence analysis results
            
        Returns:
            Executive summary narrative
        """
        try:
            # Extract key insights
            key_insights = self._extract_key_insights(convergence_data)
            
            prompt = f"""
            Generate a concise executive summary for {self.ticker} options convergence analysis. Use the specific data provided:

            ANALYSIS PERIOD: {key_insights['date_range']}
            TOTAL DATES ANALYZED: {key_insights['total_analysis_dates']}

            KEY FINDINGS:
            {json.dumps(key_insights, indent=2)}

            EXECUTIVE SUMMARY REQUIREMENTS:
            1. Lead with the most critical convergence pressure findings and dates
            2. Quantify the gamma exposure and theta decay risks using actual numbers
            3. Highlight the highest risk periods with specific dates and pressure levels
            4. Provide clear risk assessment for different market participants
            5. Include specific trading implications based on the Greeks analysis

            Use actual numerical data from the analysis. Reference specific dates, pressure levels, and Greek exposures.
            Keep professional, quantitative, and under 400 words. This is for senior risk management and trading executives.
            """
            
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a chief risk officer preparing an executive briefing on options market analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=400,
                temperature=0.5
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            return "Executive Summary: Comprehensive SPX convergence analysis completed successfully."
    
    def _prepare_data_summary(self, convergence_data: pd.DataFrame,
                            summary_stats: Dict) -> Dict:
        """Prepare comprehensive data summary for ChatGPT analysis"""

        # Get key convergence metrics with detailed statistics
        key_metrics = {}

        # Gamma convergence metrics
        gamma_metrics = [
            'gamma_total_contracts', 'gamma_atm_contracts', 'gamma_convergence_pressure',
            'gamma_total_gamma_exposure', 'gamma_gamma_concentration_ratio', 'gamma_pin_risk_indicator'
        ]

        # Theta decay metrics
        theta_metrics = [
            'theta_total_contracts', 'theta_time_decay_pressure', 'theta_total_theta_exposure',
            'theta_theta_acceleration', 'theta_atm_theta_ratio'
        ]

        # Vega sensitivity metrics
        vega_metrics = [
            'vega_total_contracts', 'vega_total_vega_exposure', 'vega_avg_implied_vol',
            'vega_iv_skew', 'vega_vol_risk_premium'
        ]

        # Portfolio Greeks
        portfolio_metrics = [
            'total_delta', 'total_gamma', 'total_vega', 'total_theta', 'gex'
        ]

        all_metrics = gamma_metrics + theta_metrics + vega_metrics + portfolio_metrics

        for col in all_metrics:
            if col in convergence_data.columns:
                series = convergence_data[col]
                key_metrics[col] = {
                    'mean': float(series.mean()),
                    'max': float(series.max()),
                    'min': float(series.min()),
                    'std': float(series.std()),
                    'max_date': str(convergence_data.loc[series.idxmax(), 'date']),
                    'min_date': str(convergence_data.loc[series.idxmin(), 'date'])
                }

        # Add specific high-level insights
        insights = {}
        if 'gamma_convergence_pressure' in convergence_data.columns:
            max_pressure = convergence_data['gamma_convergence_pressure'].max()
            max_pressure_date = convergence_data.loc[convergence_data['gamma_convergence_pressure'].idxmax(), 'date']
            insights['highest_convergence_pressure'] = {
                'value': float(max_pressure),
                'date': str(max_pressure_date)
            }

        if 'theta_time_decay_pressure' in convergence_data.columns:
            max_theta = convergence_data['theta_time_decay_pressure'].max()
            max_theta_date = convergence_data.loc[convergence_data['theta_time_decay_pressure'].idxmax(), 'date']
            insights['highest_theta_decay'] = {
                'value': float(max_theta),
                'date': str(max_theta_date)
            }

        return {
            'analysis_period': f"{str(convergence_data['date'].min())} to {str(convergence_data['date'].max())}",
            'total_dates': len(convergence_data),
            'analysis_types': convergence_data['analysis_type'].value_counts().to_dict(),
            'key_metrics': key_metrics,
            'critical_insights': insights,
            'data_summary': self._convert_timestamps_to_strings(summary_stats),
            'date_range_insights': self._get_date_range_insights(convergence_data)
        }
    
    def _create_analysis_prompt(self, data_summary: Dict) -> str:
        """Create comprehensive analysis prompt for ChatGPT"""

        return f"""
        You are analyzing {self.ticker} options convergence data from a sophisticated quantitative analysis system. Generate a comprehensive professional narrative based on the following detailed metrics:

        ANALYSIS OVERVIEW:
        - Period: {data_summary['analysis_period']}
        - Total Trading Dates Analyzed: {data_summary['total_dates']}
        - Analysis Framework: Comprehensive multi-Greek convergence analysis (Gamma, Theta, Vega)

        CRITICAL INSIGHTS:
        {json.dumps(data_summary.get('critical_insights', {}), indent=2)}

        DETAILED CONVERGENCE METRICS:
        {json.dumps(data_summary['key_metrics'], indent=2)}

        MARKET DATA CHARACTERISTICS:
        {json.dumps(data_summary['data_summary'], indent=2)}

        TEMPORAL ANALYSIS INSIGHTS:
        {json.dumps(data_summary['date_range_insights'], indent=2)}

        SPECIFIC ANALYSIS REQUIREMENTS:
        Generate a narrative that addresses:

        1. CONVERGENCE PATTERNS: Analyze the gamma convergence pressure patterns, ATM concentration ratios, and pin risk indicators. Explain what these metrics reveal about market maker positioning and potential price magnetism effects.

        2. THETA DECAY DYNAMICS: Examine the theta decay pressure, acceleration patterns, and time decay concentration. Discuss implications for options sellers vs buyers and optimal timing strategies.

        3. VEGA SENSITIVITY ANALYSIS: Review vega exposure patterns, implied volatility skew, and volatility risk premiums. Explain how these affect options pricing and volatility trading opportunities.

        4. PORTFOLIO GREEKS IMPACT: Analyze the total delta, gamma, vega, theta exposures and their implications for market stability and potential volatility events.

        5. RISK CONCENTRATION: Identify periods of highest convergence pressure and explain the associated risks for different market participants.

        6. TRADING IMPLICATIONS: Provide specific insights for:
           - Market makers managing inventory
           - Institutional options traders
           - Volatility traders
           - Risk managers

        Use the actual numerical data provided to support your analysis. Reference specific dates, pressure levels, and Greek values. Write in a professional, quantitative tone suitable for sophisticated institutional investors and options professionals.
        """
    
    def _create_date_prompt(self, date: str, date_data: Dict) -> str:
        """Create date-specific analysis prompt"""
        
        return f"""
        Analyze the {self.ticker} options convergence data for {date}:

        Analysis Type: {date_data.get('analysis_type', 'N/A')}
        Total Contracts: {date_data.get('gamma_total_contracts', 'N/A')}
        Primary Greek: {date_data.get('expected_primary_greek', 'N/A')}

        Key Metrics:
        - Gamma Exposure: {date_data.get('gamma_total_gamma_exposure', 'N/A')}
        - Convergence Pressure: {date_data.get('gamma_convergence_pressure', 'N/A')}
        - Theta Decay Pressure: {date_data.get('theta_time_decay_pressure', 'N/A')}
        - Spot Price: ${date_data.get('spot_price', 'N/A')}

        Provide a concise analysis focusing on:
        1. What convergence patterns were observed
        2. Key risk factors for this date
        3. Trading implications

        Keep it under 150 words and actionable.
        """
    
    def _extract_key_insights(self, convergence_data: pd.DataFrame) -> Dict:
        """Extract key insights for executive summary"""
        
        insights = {
            'total_analysis_dates': len(convergence_data),
            'date_range': f"{str(convergence_data['date'].min())} to {str(convergence_data['date'].max())}",
            'dominant_analysis_type': convergence_data['analysis_type'].mode().iloc[0] if not convergence_data.empty else 'N/A'
        }
        
        # Find highest risk dates
        if 'gamma_convergence_pressure' in convergence_data.columns:
            max_pressure_idx = convergence_data['gamma_convergence_pressure'].idxmax()
            insights['highest_risk_date'] = {
                'date': convergence_data.loc[max_pressure_idx, 'date'],
                'pressure': float(convergence_data.loc[max_pressure_idx, 'gamma_convergence_pressure'])
            }
        
        # Average contract volume
        if 'gamma_total_contracts' in convergence_data.columns:
            insights['avg_daily_contracts'] = int(convergence_data['gamma_total_contracts'].mean())
        
        return insights
    
    def _get_date_range_insights(self, convergence_data: pd.DataFrame) -> Dict:
        """Get insights by date ranges"""

        convergence_data['date'] = pd.to_datetime(convergence_data['date'])

        # Group by month and convert to string keys
        monthly_stats = convergence_data.groupby(convergence_data['date'].dt.to_period('M')).agg({
            'gamma_total_contracts': 'mean' if 'gamma_total_contracts' in convergence_data.columns else lambda x: 0,
            'gamma_convergence_pressure': 'mean' if 'gamma_convergence_pressure' in convergence_data.columns else lambda x: 0
        }).round(2)

        # Convert Period index to strings for JSON serialization
        monthly_dict = {}
        for period, row in monthly_stats.iterrows():
            monthly_dict[str(period)] = row.to_dict()

        return monthly_dict

    def _convert_timestamps_to_strings(self, data):
        """Convert pandas Timestamps and other non-serializable objects to strings for JSON serialization"""
        if isinstance(data, dict):
            return {str(k): self._convert_timestamps_to_strings(v) for k, v in data.items()}
        elif isinstance(data, (list, tuple)):
            return [self._convert_timestamps_to_strings(item) for item in data]
        elif hasattr(data, 'strftime'):  # Timestamp-like objects
            return str(data)
        elif hasattr(data, '__str__') and not isinstance(data, (int, float, bool, str, type(None))):
            # Convert other non-serializable objects to strings
            return str(data)
        else:
            return data

    def _generate_fallback_narrative(self, convergence_data: pd.DataFrame,
                                   summary_stats: Dict) -> str:
        """Generate fallback narrative if OpenAI fails"""
        
        total_dates = len(convergence_data)
        date_range = f"{convergence_data['date'].min()} to {convergence_data['date'].max()}"
        
        return f"""
        SPX Convergence Analysis Summary
        
        Analysis Period: {date_range}
        Total Dates Analyzed: {total_dates}
        
        This comprehensive analysis examined SPX options convergence patterns across {total_dates} trading dates. 
        The analysis employed advanced statistical methods to identify gamma convergence, theta decay, and vega 
        sensitivity patterns in the options market.
        
        Key findings include identification of convergence pressure points, risk concentration areas, and 
        optimal trading opportunities based on Greeks behavior and market microstructure analysis.
        
        Note: Detailed narrative generation temporarily unavailable. Please refer to the quantitative 
        results in the accompanying data files for specific metrics and insights.
        """
