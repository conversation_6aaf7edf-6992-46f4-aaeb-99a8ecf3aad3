"""
Configuration settings for SPX Convergence Analysis
"""
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
REPORTS_DIR = PROJECT_ROOT / "reports"

# External data paths - dynamically find most recent data location
OPTION_HISTORY_BASE = Path("/Users/<USER>/Downloads/optionhistory")

def get_latest_data_info():
    """Find the most recent year and quarter from available data directories."""
    if not OPTION_HISTORY_BASE.exists():
        raise FileNotFoundError(f"Option history directory not found: {OPTION_HISTORY_BASE}")

    # Find all directories matching the pattern YYYY_qN_option_chain
    data_dirs = []
    for item in OPTION_HISTORY_BASE.iterdir():
        if item.is_dir() and item.name.endswith('_option_chain'):
            # Extract year and quarter from directory name (e.g., "2025_q2_option_chain")
            parts = item.name.replace('_option_chain', '').split('_')
            if len(parts) == 2:
                try:
                    year = int(parts[0])
                    quarter = parts[1]  # e.g., "q2"
                    data_dirs.append((year, quarter, item))
                except ValueError:
                    continue

    if not data_dirs:
        raise FileNotFoundError("No valid data directories found")

    # Sort by year and quarter to get the most recent
    data_dirs.sort(key=lambda x: (x[0], x[1]))  # Sort by year, then quarter
    latest_year, latest_quarter, latest_dir = data_dirs[-1]

    return latest_year, latest_quarter, latest_dir

# Get current data information
CURRENT_YEAR, CURRENT_QUARTER, CURRENT_DATA_DIR = get_latest_data_info()

# Data files
SPX_DATA_FILE = CURRENT_DATA_DIR / f"spx_complete_{CURRENT_YEAR}_{CURRENT_QUARTER}.csv"

# Analysis parameters
RISK_FREE_RATE = 0.05  # 5% risk-free rate
DIVIDEND_YIELD = 0.015  # 1.5% dividend yield

# Convergence analysis dates (using available data from June 2025)
CONVERGENCE_DATES = [
    '2025-06-02',  # Early June analysis
    '2025-06-05',  # Week 1 end
    '2025-06-10',  # Mid-week analysis
    '2025-06-13',  # Week 2 end
    '2025-06-17',  # Mid-month analysis
    '2025-06-20',  # Week 3 end
    '2025-06-24',  # Late month analysis
    '2025-06-25'   # Final available date
]

# Expected outcomes (modified for available dates - for demonstration)
EXPECTED_OUTCOMES = {
    '2025-06-02': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-05': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-10': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-13': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-17': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-20': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-24': {'direction': 'ANALYSIS', 'magnitude': (0, 0)},
    '2025-06-25': {'direction': 'ANALYSIS', 'magnitude': (0, 0)}
}

