"""
Configuration settings for Options Convergence Analysis
"""
from pathlib import Path

# Ticker Configuration - Default to SPX, can be changed to VIX, TLT, etc.
TICKER = "VIX"  # Change this to analyze different tickers (VIX, TLT, etc.)

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
REPORTS_DIR = PROJECT_ROOT / "reports"

# External data paths - dynamically find most recent data location
OPTION_HISTORY_BASE = Path("/Users/<USER>/Downloads/optionhistory")

def get_latest_data_info():
    """Find the most recent year and quarter from available data directories."""
    if not OPTION_HISTORY_BASE.exists():
        raise FileNotFoundError(f"Option history directory not found: {OPTION_HISTORY_BASE}")

    # Find all directories matching the pattern YYYY_qN_option_chain
    data_dirs = []
    for item in OPTION_HISTORY_BASE.iterdir():
        if item.is_dir() and item.name.endswith('_option_chain'):
            # Extract year and quarter from directory name (e.g., "2025_q2_option_chain")
            parts = item.name.replace('_option_chain', '').split('_')
            if len(parts) == 2:
                try:
                    year = int(parts[0])
                    quarter = parts[1]  # e.g., "q2"
                    data_dirs.append((year, quarter, item))
                except ValueError:
                    continue

    if not data_dirs:
        raise FileNotFoundError("No valid data directories found")

    # Sort by year and quarter to get the most recent
    # Convert quarter string to number for proper sorting (q1=1, q2=2, etc.)
    def quarter_to_num(quarter_str):
        return int(quarter_str[1:]) if quarter_str.startswith('q') else 0

    data_dirs.sort(key=lambda x: (x[0], quarter_to_num(x[1])))  # Sort by year, then quarter number
    latest_year, latest_quarter, latest_dir = data_dirs[-1]

    return latest_year, latest_quarter, latest_dir

# Get current data information
CURRENT_YEAR, CURRENT_QUARTER, CURRENT_DATA_DIR = get_latest_data_info()

# Data files - Dynamic based on ticker
DATA_FILE = CURRENT_DATA_DIR / f"{TICKER.lower()}_complete_{CURRENT_YEAR}_{CURRENT_QUARTER}.csv"

# Analysis parameters
RISK_FREE_RATE = 0.05  # 5% risk-free rate
DIVIDEND_YIELD = 0.015  # 1.5% dividend yield

# OpenAI Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# Dynamic data-driven analysis configuration
def generate_analysis_dates_and_targets():
    """
    Generate FORWARD-LOOKING convergence analysis dates and expected outcomes
    Focuses on future expiration dates and current open interest for trading decisions
    Returns tuple of (convergence_dates, expected_outcomes)
    """
    import pandas as pd
    from datetime import datetime

    try:
        # Load data to analyze current positions and future expirations
        df = pd.read_csv(DATA_FILE)
        df['date'] = pd.to_datetime(df['date'])
        df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
        df['dte'] = (df['expiry_date'] - df['date']).dt.days

        # Dynamic column name based on ticker
        close_col = f'{TICKER.lower()}_close'
        df['moneyness'] = df[close_col] / df['Strike']

        # Get the most recent trading date (latest data available)
        latest_trading_date = df['date'].max()
        print(f"Using latest trading date: {latest_trading_date.date()}")

        # Filter to only the most recent trading date to see current positions
        current_positions = df[df['date'] == latest_trading_date].copy()

        # TODAY'S PERSPECTIVE: Only analyze options that haven't expired yet
        today = datetime.now()
        active_options = current_positions[current_positions['expiry_date'] > today].copy()

        if len(active_options) == 0:
            print("Warning: No active options found for forward-looking analysis")
            # Fallback to most recent data
            active_options = current_positions.copy()

        print(f"Found {len(active_options)} active options positions for forward analysis")

        # Get future expiration dates (convergence targets)
        future_expiries = sorted(active_options['expiry_date'].dt.date.unique())
        print(f"Future expiration dates: {future_expiries[:10]}")  # Show first 10

        # Strategy: Focus on upcoming expiration dates as convergence events
        # These are the dates when convergence pressure will be highest
        convergence_target_dates = []

        # Add the next 20 expiration dates as convergence analysis targets
        for exp_date in future_expiries[:20]:
            convergence_target_dates.append(exp_date)

        # If we don't have enough future expiries, add some recent trading dates
        if len(convergence_target_dates) < 20:
            recent_dates = sorted(df['date'].dt.date.unique())[-20:]
            for date in recent_dates:
                if date not in convergence_target_dates:
                    convergence_target_dates.append(date)
                if len(convergence_target_dates) >= 20:
                    break

        # Sort and take the most relevant 20 dates
        selected_dates = sorted(convergence_target_dates)[:20]

        # Generate FORWARD-LOOKING expected outcomes based on current positions
        expected_outcomes = {}

        for date in selected_dates:
            date_str = date.strftime('%Y-%m-%d')

            # For expiration dates, analyze the options expiring on that date
            if hasattr(date, 'date'):
                date_obj = date.date()
            else:
                date_obj = date
            expiring_options = active_options[active_options['expiry_date'].dt.date == date_obj]

            # For trading dates, use the data from that date if available
            if len(expiring_options) == 0:
                date_data = df[df['date'].dt.date == date_obj]
            else:
                date_data = expiring_options

            if len(date_data) == 0:
                # Use current active options as fallback
                date_data = active_options.sample(min(100, len(active_options))) if len(active_options) > 0 else active_options

            if len(date_data) == 0:
                continue

            # Calculate forward-looking metrics from TODAY'S perspective
            current_date = latest_trading_date
            target_date = pd.to_datetime(date_obj)
            days_to_target = (target_date - current_date).days

            # Analyze current open interest and positioning
            total_open_interest = date_data['Open Interest'].sum()
            gamma_exposure = date_data['Gamma'].sum() * date_data['Open Interest'].sum()
            atm_contracts = len(date_data[abs(date_data['moneyness'] - 1.0) < 0.05])  # 5% ATM range
            total_contracts = len(date_data)

            # Determine convergence type based on time to target and current positioning
            if days_to_target <= 7 or date_obj in future_expiries[:5]:
                direction = 'GAMMA_PIN_CONVERGENCE'  # Near-term expiry = gamma pinning
                magnitude_factor = min(gamma_exposure / 100000, 5.0)
            elif days_to_target <= 21:
                direction = 'THETA_DECAY_ACCELERATION'  # Medium-term = theta acceleration
                magnitude_factor = min(total_open_interest / 50000, 3.0)
            elif days_to_target <= 45:
                direction = 'VEGA_SENSITIVITY_SHIFT'  # Longer-term = vega changes
                magnitude_factor = min(date_data['Vega'].sum() / 10000, 2.0)
            else:
                direction = 'DELTA_HEDGING_FLOW'  # Long-term = delta flows
                magnitude_factor = min(abs(date_data['Delta'].mean()) * total_open_interest / 10000, 1.5)

            expected_outcomes[date_str] = {
                'direction': direction,
                'magnitude': (magnitude_factor * 0.8, magnitude_factor * 1.2),
                'primary_greek': 'gamma' if days_to_target <= 7 else 'theta' if days_to_target <= 21 else 'vega' if days_to_target <= 45 else 'delta',
                'days_to_target': days_to_target,
                'total_open_interest': int(total_open_interest),
                'contract_count': total_contracts,
                'atm_ratio': round(atm_contracts / total_contracts, 3) if total_contracts > 0 else 0,
                'is_expiration_date': date_obj in future_expiries,
                'gamma_exposure': round(gamma_exposure, 2)
            }

        convergence_dates = [d.strftime('%Y-%m-%d') for d in selected_dates]

        return convergence_dates, expected_outcomes

    except Exception as e:
        # Fallback to a minimal set if data analysis fails
        print(f"Warning: Could not generate dynamic dates ({e}), using fallback")
        fallback_dates = ['2025-06-20', '2025-06-25']  # Major expiry dates
        fallback_outcomes = {
            date: {'direction': 'ANALYSIS_REQUIRED', 'magnitude': (0, 0)}
            for date in fallback_dates
        }
        return fallback_dates, fallback_outcomes

# Generate dynamic analysis configuration
CONVERGENCE_DATES, EXPECTED_OUTCOMES = generate_analysis_dates_and_targets()

