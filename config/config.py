"""
Configuration settings for Options Convergence Analysis
"""
from pathlib import Path

# Ticker Configuration - Default to SPX, can be changed to VIX, TLT, etc.
TICKER = "VIX"  # Change this to analyze different tickers (VIX, TLT, etc.)

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
REPORTS_DIR = PROJECT_ROOT / "reports"

# External data paths - dynamically find most recent data location
OPTION_HISTORY_BASE = Path("/Users/<USER>/Downloads/optionhistory")

def get_latest_data_info():
    """Find the most recent year and quarter from available data directories."""
    if not OPTION_HISTORY_BASE.exists():
        raise FileNotFoundError(f"Option history directory not found: {OPTION_HISTORY_BASE}")

    # Find all directories matching the pattern YYYY_qN_option_chain
    data_dirs = []
    for item in OPTION_HISTORY_BASE.iterdir():
        if item.is_dir() and item.name.endswith('_option_chain'):
            # Extract year and quarter from directory name (e.g., "2025_q2_option_chain")
            parts = item.name.replace('_option_chain', '').split('_')
            if len(parts) == 2:
                try:
                    year = int(parts[0])
                    quarter = parts[1]  # e.g., "q2"
                    data_dirs.append((year, quarter, item))
                except ValueError:
                    continue

    if not data_dirs:
        raise FileNotFoundError("No valid data directories found")

    # Sort by year and quarter to get the most recent
    data_dirs.sort(key=lambda x: (x[0], x[1]))  # Sort by year, then quarter
    latest_year, latest_quarter, latest_dir = data_dirs[-1]

    return latest_year, latest_quarter, latest_dir

# Get current data information
CURRENT_YEAR, CURRENT_QUARTER, CURRENT_DATA_DIR = get_latest_data_info()

# Data files - Dynamic based on ticker
DATA_FILE = CURRENT_DATA_DIR / f"{TICKER.lower()}_complete_{CURRENT_YEAR}_{CURRENT_QUARTER}.csv"

# Legacy variable for backward compatibility
SPX_DATA_FILE = DATA_FILE

# Analysis parameters
RISK_FREE_RATE = 0.05  # 5% risk-free rate
DIVIDEND_YIELD = 0.015  # 1.5% dividend yield

# OpenAI Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# Dynamic data-driven analysis configuration
def generate_analysis_dates_and_targets():
    """
    Generate convergence analysis dates and expected outcomes dynamically from data
    Returns tuple of (convergence_dates, expected_outcomes)
    """
    import pandas as pd
    from datetime import timedelta

    try:
        # Load data to analyze available dates and patterns
        df = pd.read_csv(DATA_FILE)
        df['date'] = pd.to_datetime(df['date'])
        df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
        df['dte'] = (df['expiry_date'] - df['date']).dt.days

        # Dynamic column name based on ticker
        close_col = f'{TICKER.lower()}_close'
        df['moneyness'] = df[close_col] / df['Strike']

        # Get unique dates sorted
        available_dates = sorted(df['date'].dt.date.unique())

        # Strategy 1: Select dates based on major expiration events
        # Find dates with high options activity (convergence opportunities)
        daily_activity = df.groupby(df['date'].dt.date).agg({
            'Strike': 'count',  # Total contracts
            'Open Interest': 'sum',  # Total open interest
            'Volume': 'sum',  # Total volume
            'dte': lambda x: (x <= 7).sum(),  # Near-expiry contracts
            'Gamma': 'sum'  # Total gamma exposure
        }).round(2)

        # Strategy 2: Focus on expiration weeks (high gamma, convergence effects)
        expiry_dates = df['expiry_date'].dt.date.unique()
        expiry_weeks = []

        for exp_date in expiry_dates:
            # Find trading dates in the week leading to expiration
            week_start = exp_date - timedelta(days=7)
            week_dates = [d for d in available_dates if week_start <= d <= exp_date]
            if len(week_dates) >= 3:  # Need at least 3 trading days
                expiry_weeks.extend(week_dates[-3:])  # Last 3 days before expiry

        # Strategy 3: Select high-activity dates with diverse DTE ranges
        # Score dates based on multiple factors
        daily_activity['convergence_score'] = (
            daily_activity['Strike'] / daily_activity['Strike'].max() * 0.3 +  # Activity volume
            daily_activity['dte'] / daily_activity['dte'].max() * 0.2 +  # Near-expiry concentration
            daily_activity['Gamma'] / daily_activity['Gamma'].max() * 0.3 +  # Gamma exposure
            daily_activity['Volume'] / daily_activity['Volume'].max() * 0.2   # Trading volume
        )

        # Select top scoring dates, ensuring good distribution across time
        top_dates = daily_activity.nlargest(20, 'convergence_score').index.tolist()

        # Combine strategies and remove duplicates
        selected_dates = list(set(expiry_weeks + top_dates))
        selected_dates = sorted(selected_dates)[:15]  # Limit to 15 dates for analysis

        # Generate expected outcomes based on data patterns
        expected_outcomes = {}

        for date in selected_dates:
            date_str = date.strftime('%Y-%m-%d')
            date_data = df[df['date'].dt.date == date]

            if len(date_data) == 0:
                continue

            # Analyze the data characteristics for this date
            avg_dte = date_data['dte'].mean()
            gamma_concentration = date_data['Gamma'].sum()
            atm_contracts = len(date_data[abs(date_data['moneyness'] - 1.0) < 0.02])
            total_contracts = len(date_data)

            # Determine expected convergence characteristics
            if avg_dte <= 7:
                direction = 'HIGH_GAMMA_CONVERGENCE'
                magnitude_factor = min(gamma_concentration / 1000, 5.0)
            elif avg_dte <= 21:
                direction = 'THETA_DECAY_CONVERGENCE'
                magnitude_factor = min(atm_contracts / total_contracts * 10, 3.0)
            elif avg_dte <= 45:
                direction = 'VEGA_CONVERGENCE'
                magnitude_factor = min(date_data['Vega'].sum() / 10000, 2.0)
            else:
                direction = 'DELTA_CONVERGENCE'
                magnitude_factor = min(abs(date_data['Delta'].mean()) * 5, 1.5)

            expected_outcomes[date_str] = {
                'direction': direction,
                'magnitude': (magnitude_factor * 0.8, magnitude_factor * 1.2),
                'primary_greek': 'gamma' if avg_dte <= 7 else 'theta' if avg_dte <= 21 else 'vega' if avg_dte <= 45 else 'delta',
                'avg_dte': round(avg_dte, 1),
                'contract_count': total_contracts,
                'atm_ratio': round(atm_contracts / total_contracts, 3)
            }

        convergence_dates = [d.strftime('%Y-%m-%d') for d in selected_dates]

        return convergence_dates, expected_outcomes

    except Exception as e:
        # Fallback to a minimal set if data analysis fails
        print(f"Warning: Could not generate dynamic dates ({e}), using fallback")
        fallback_dates = ['2025-06-20', '2025-06-25']  # Major expiry dates
        fallback_outcomes = {
            date: {'direction': 'ANALYSIS_REQUIRED', 'magnitude': (0, 0)}
            for date in fallback_dates
        }
        return fallback_dates, fallback_outcomes

# Generate dynamic analysis configuration
CONVERGENCE_DATES, EXPECTED_OUTCOMES = generate_analysis_dates_and_targets()

