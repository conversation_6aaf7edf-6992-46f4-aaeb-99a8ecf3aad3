"""
Configuration settings for Options Convergence Analysis
"""
from pathlib import Path
import pandas as pd

# Ticker Configuration - Default to SPX, can be changed to VIX, TLT, etc.
TICKER = "SPX"  # Change this to analyze different tickers (VIX, TLT, etc.)

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
REPORTS_DIR = PROJECT_ROOT / "reports"

# External data paths - dynamically find most recent data location
OPTION_HISTORY_BASE = Path("/Users/<USER>/Downloads/optionhistory")

def get_latest_data_info():
    """Get the most recent data directory and info"""
    if not OPTION_HISTORY_BASE.exists():
        raise FileNotFoundError(f"Option history base directory not found: {OPTION_HISTORY_BASE}")
    
    # Find all year_quarter directories
    data_dirs = []
    for item in OPTION_HISTORY_BASE.iterdir():
        if item.is_dir() and "_" in item.name:
            try:
                year_str, quarter_str = item.name.split("_")
                if year_str.isdigit() and quarter_str.startswith("q"):
                    year = int(year_str)
                    quarter = int(quarter_str[1:])
                    data_dirs.append((year, quarter, item))
            except (ValueError, IndexError):
                continue
    
    if not data_dirs:
        raise FileNotFoundError("No valid data directories found")
    
    # Sort by year and quarter to get the most recent
    data_dirs.sort(key=lambda x: (x[0], x[1]), reverse=True)
    latest_year, latest_quarter, latest_dir = data_dirs[0]
    
    return latest_year, f"q{latest_quarter}", latest_dir

# Get current data information
CURRENT_YEAR, CURRENT_QUARTER, CURRENT_DATA_DIR = get_latest_data_info()

# Data files - Dynamic based on ticker
DATA_FILE = CURRENT_DATA_DIR / f"{TICKER.lower()}_complete_{CURRENT_YEAR}_{CURRENT_QUARTER}.csv"

# Analysis parameters
RISK_FREE_RATE = 0.05  # 5% risk-free rate
DIVIDEND_YIELD = 0.015  # 1.5% dividend yield

# OpenAI Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

def generate_analysis_dates_and_targets():
    """
    Generate forward-looking analysis dates and expected outcomes based on current market data
    This creates a dynamic analysis framework that adapts to current market conditions
    """
    try:
        # Load data to analyze current positions and future expirations
        df = pd.read_csv(DATA_FILE)
        df['date'] = pd.to_datetime(df['date'])
        df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
        df['dte'] = (df['expiry_date'] - df['date']).dt.days

        # Dynamic column name based on ticker
        close_col = f'{TICKER.lower()}_close'
        df['moneyness'] = df[close_col] / df['Strike']

        # Get the most recent trading date (latest data available)
        latest_trading_date = df['date'].max()
        print(f"Using latest trading date: {latest_trading_date.date()}")

        # Filter to only the most recent trading date to see current positions
        current_positions = df[df['date'] == latest_trading_date].copy()

        # Filter to active options (positive DTE and reasonable moneyness)
        active_options = current_positions[
            (current_positions['dte'] > 0) & 
            (current_positions['dte'] <= 90) &  # Within 90 days
            (current_positions['moneyness'] > 0.7) & 
            (current_positions['moneyness'] < 1.3)  # Within reasonable moneyness range
        ].copy()

        print(f"Found {len(active_options)} active options positions for forward analysis")

        # Get future expiration dates (convergence targets)
        future_expiries = sorted(active_options['expiry_date'].dt.date.unique())
        print(f"Future expiration dates: {future_expiries[:10]}")  # Show first 10

        # Strategy: Focus on upcoming expiration dates as convergence events
        # These are the dates when convergence pressure will be highest
        convergence_target_dates = []

        # Add the next 20 expiration dates as convergence analysis targets
        for exp_date in future_expiries[:20]:
            convergence_target_dates.append(exp_date)

        # Generate expected outcomes based on current market positioning
        expected_outcomes = {}
        
        # Analyze each target date for expected convergence patterns
        for date_obj in convergence_target_dates:
            date_str = date_obj.strftime('%Y-%m-%d')
            
            # Get options data for this expiration date
            date_data = active_options[active_options['expiry_date'].dt.date == date_obj].copy()
            
            if len(date_data) == 0:
                continue

            # Calculate forward-looking metrics from TODAY'S perspective
            current_date = latest_trading_date
            target_date = pd.to_datetime(date_obj)
            days_to_target = (target_date - current_date).days

            # Analyze current open interest and positioning
            total_open_interest = date_data['Open Interest'].sum()
            gamma_exposure = date_data['Gamma'].sum() * date_data['Open Interest'].sum()
            atm_contracts = len(date_data[abs(date_data['moneyness'] - 1.0) < 0.05])  # 5% ATM range
            total_contracts = len(date_data)

            # Determine convergence type based on time to target and current positioning
            if days_to_target <= 7 or date_obj in future_expiries[:5]:
                direction = 'GAMMA_PIN_CONVERGENCE'  # Near-term expiry = gamma pinning
                magnitude_factor = min(gamma_exposure / 100000, 5.0)
            elif days_to_target <= 21:
                direction = 'THETA_DECAY_ACCELERATION'  # Medium-term = theta acceleration
                magnitude_factor = min(total_open_interest / 50000, 3.0)
            elif days_to_target <= 45:
                direction = 'VEGA_SENSITIVITY_SHIFT'  # Longer-term = vega changes
                magnitude_factor = min(date_data['Vega'].sum() / 10000, 2.0)
            else:
                direction = 'DELTA_HEDGING_FLOW'  # Long-term = delta flows
                magnitude_factor = min(abs(date_data['Delta'].mean()) * total_open_interest / 10000, 1.5)

            expected_outcomes[date_str] = {
                'direction': direction,
                'magnitude': (magnitude_factor * 0.8, magnitude_factor * 1.2),
                'primary_greek': 'gamma' if days_to_target <= 7 else 'theta' if days_to_target <= 21 else 'vega' if days_to_target <= 45 else 'delta',
                'days_to_target': days_to_target,
                'total_open_interest': int(total_open_interest),
                'contract_count': total_contracts,
                'atm_ratio': round(atm_contracts / total_contracts, 3) if total_contracts > 0 else 0,
                'is_expiration_date': date_obj in future_expiries,
                'gamma_exposure': round(gamma_exposure, 2)
            }

        convergence_dates = [d.strftime('%Y-%m-%d') for d in convergence_target_dates[:20]]

        return convergence_dates, expected_outcomes

    except Exception as e:
        # Fallback to a minimal set if data analysis fails
        print(f"Warning: Could not generate dynamic dates ({e}), using fallback")
        fallback_dates = ['2025-07-15', '2025-07-31']  # Major expiry dates
        fallback_outcomes = {
            date: {'direction': 'ANALYSIS_REQUIRED', 'magnitude': (0, 0)}
            for date in fallback_dates
        }
        return fallback_dates, fallback_outcomes

# Generate dynamic analysis configuration
CONVERGENCE_DATES, EXPECTED_OUTCOMES = generate_analysis_dates_and_targets()
