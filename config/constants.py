"""
Constants Configuration for Options Convergence Analysis
Centralized location for all magic numbers, thresholds, and configuration values
"""

# =============================================================================
# ANALYSIS THRESHOLDS AND RANGES
# =============================================================================

# Moneyness and Strike Analysis
MONEYNESS_ATM_THRESHOLD = 0.05  # 5% around current price for ATM classification
STRIKE_ANALYSIS_RANGE = 0.10    # 10% around current price for key strikes analysis
PRICE_PROXIMITY_RANGE = 0.05    # 5% range for ATM options in charm calculation

# Days to Expiration (DTE) Ranges
DTE_NEAR_TERM_MAX = 7           # Very near-term options (weekend risk)
DTE_SHORT_TERM_MAX = 14         # Short-term options (high time decay)
DTE_MEDIUM_TERM_MIN = 14        # Medium-term options minimum
DTE_MEDIUM_TERM_MAX = 28        # Medium-term options maximum
DTE_LONG_TERM_MIN = 22          # Long-term options minimum
DTE_LONG_TERM_MAX = 90          # Long-term options maximum
DTE_TIME_DECAY_THRESHOLD = 30   # Threshold for time decay factor calculation

# =============================================================================
# SIGNAL DETECTION THRESHOLDS
# =============================================================================

# Convergence Pressure Thresholds
CONVERGENCE_PRESSURE_LOW = 0.70         # Low convergence pressure threshold
CONVERGENCE_PRESSURE_MODERATE = 0.75    # Moderate convergence pressure
CONVERGENCE_PRESSURE_HIGH = 0.80        # High convergence pressure
CONVERGENCE_PRESSURE_EXTREME = 0.82     # Extreme convergence pressure
CONVERGENCE_PRESSURE_EXPLOSIVE = 0.83   # Explosive signal threshold

# Gamma Exposure Normalization
GAMMA_EXPOSURE_SPX_MAX = 10000          # Typical max gamma exposure for SPX
GAMMA_EXPOSURE_NORMALIZATION_CAP = 1.0  # Cap for normalized gamma

# Signal Strength Calculation Weights
SIGNAL_WEIGHT_CONVERGENCE = 5           # Weight for convergence pressure in signal strength
SIGNAL_WEIGHT_GAMMA = 3                 # Weight for gamma exposure in signal strength  
SIGNAL_WEIGHT_TIME = 2                  # Weight for time factor in signal strength
SIGNAL_STRENGTH_MAX = 10.0              # Maximum signal strength value

# Bearish Signal Conditions
BEARISH_CONVERGENCE_HIGH = 0.80         # High convergence for bearish signal
BEARISH_CONVERGENCE_MODERATE = 0.75     # Moderate convergence for bearish signal
BEARISH_DTE_SHORT = 14                  # Short DTE for bearish conditions
BEARISH_DTE_VERY_SHORT = 7              # Very short DTE for bearish conditions
BEARISH_DTE_MEDIUM = 21                 # Medium DTE for bearish conditions
BEARISH_GAMMA_THRESHOLD = 0.2           # Gamma threshold for bearish signal

# Bullish Signal Conditions  
BULLISH_CONVERGENCE_MIN = 0.70          # Minimum convergence for bullish signal
BULLISH_CONVERGENCE_MAX = 0.82          # Maximum convergence for bullish signal
BULLISH_DTE_MIN = 14                    # Minimum DTE for bullish signal
BULLISH_DTE_MAX = 28                    # Maximum DTE for bullish signal
BULLISH_DTE_LONG = 21                   # Long DTE threshold for bullish signal
BULLISH_GAMMA_MAX_NORMAL = 0.3          # Normal gamma threshold for bullish signal
BULLISH_GAMMA_MAX_HIGH = 0.4            # High gamma threshold for bullish signal

# =============================================================================
# DATA FILTERING AND VALIDATION
# =============================================================================

# Minimum Data Requirements
MIN_OPTIONS_FOR_ANALYSIS = 50           # Minimum options needed for analysis
MIN_ATM_OPTIONS = 10                    # Minimum ATM options for analysis
MIN_VEGA_OPTIONS = 30                   # Minimum options for vega analysis
MIN_IV_OPTIONS = 10                     # Minimum options for IV analysis

# Top Strike Selection
TOP_OI_STRIKES_COUNT = 10               # Number of top OI strikes to analyze

# =============================================================================
# GREEKS CALCULATION PARAMETERS
# =============================================================================

# Default Values for Missing Data
DEFAULT_TIME_TO_EXPIRY = 0.1            # Default TTE when missing
DEFAULT_IMPLIED_VOLATILITY = 0.2        # Default IV when missing or invalid
DEFAULT_RISK_FREE_RATE = 0.05           # Default risk-free rate
DEFAULT_DIVIDEND_YIELD = 0.015          # Default dividend yield

# Charm Calculation
CHARM_OI_NORMALIZATION = 1000           # Normalization factor for OI in charm calculation
CHARM_WEEKEND_FACTOR = 1.5              # Weekend decay factor for charm
CHARM_PRECISION_DIGITS = 4              # Decimal places for charm rounding

# =============================================================================
# SCALING AND NORMALIZATION FACTORS
# =============================================================================

# Greeks Scaling Factors
CHARM_SCALE_FACTOR = 1000               # Scale charm to thousands
VANNA_SCALE_FACTOR = 1000               # Scale vanna to thousands  
GEX_SCALE_FACTOR = 1000000              # Scale GEX to millions
VOMMA_SCALE_FACTOR = 1000000            # Scale vomma to millions
VOLUME_SCALE_FACTOR = 1000              # Scale volume metrics to thousands
VX_OI_SCALE_FACTOR = 1000               # Scale VxOI for readability
VX_OI_FINAL_SCALE = 1000000             # Final VxOI scaling to millions

# Pressure Calculation
CONVERGENCE_PRESSURE_MULTIPLIER = 1000  # Multiplier for convergence pressure calculation
CONVERGENCE_PRESSURE_CAP = 10.0         # Cap for convergence pressure
DISTANCE_WEIGHTING_FACTOR = 10          # Factor for distance weighting in pressure calculation

# =============================================================================
# OPENAI API CONFIGURATION
# =============================================================================

# Model and Token Limits
OPENAI_MODEL = "gpt-4"                  # OpenAI model to use
OPENAI_MAX_TOKENS_COMPREHENSIVE = 2000  # Max tokens for comprehensive narrative
OPENAI_MAX_TOKENS_DATE_SPECIFIC = 500   # Max tokens for date-specific narrative
OPENAI_MAX_TOKENS_EXECUTIVE = 400       # Max tokens for executive summary

# Temperature Settings (creativity vs consistency)
OPENAI_TEMP_COMPREHENSIVE = 0.7         # Temperature for comprehensive analysis
OPENAI_TEMP_DATE_SPECIFIC = 0.6         # Temperature for date-specific analysis
OPENAI_TEMP_EXECUTIVE = 0.5             # Temperature for executive summary

# Narrative Generation Control
ENABLE_COMPREHENSIVE_NARRATIVE = True   # Enable comprehensive narrative generation
ENABLE_DATE_SPECIFIC_NARRATIVES = True  # Enable date-specific narratives
ENABLE_EXECUTIVE_SUMMARY = True         # Enable executive summary generation
MAX_KEY_DATES_FOR_NARRATIVES = 5        # Maximum number of key dates for detailed narratives

# =============================================================================
# ANALYSIS CONFIGURATION
# =============================================================================

# Forward-Looking Analysis
FORWARD_ANALYSIS_DAYS_COUNT = 20        # Number of future dates to analyze
FORWARD_ANALYSIS_MAX_DAYS = 32          # Maximum days forward to analyze

# Historical Analysis (Legacy)
HISTORICAL_THETA_BEARISH_THRESHOLD = 0.4    # Bearish threshold for theta analysis
HISTORICAL_THETA_PRESSURE_HIGH = 2.0        # High theta pressure threshold
HISTORICAL_BULLISH_THRESHOLD = 0.3          # Bullish threshold for historical analysis
HISTORICAL_THETA_PRESSURE_LOW = 1.5         # Low theta pressure threshold
HISTORICAL_EXPLOSIVE_THRESHOLD = 0.6        # Explosive threshold for historical analysis
HISTORICAL_SIGNAL_STRENGTH_DIVISOR = 10     # Divisor for historical signal strength

# =============================================================================
# MONEYNESS AND VOLATILITY ANALYSIS
# =============================================================================

# Moneyness Ranges for Analysis
MONEYNESS_OTM_PUT_MAX = 0.95            # Maximum moneyness for OTM puts
MONEYNESS_OTM_CALL_MIN = 1.05           # Minimum moneyness for OTM calls
MONEYNESS_ATM_LOWER = 0.95              # Lower bound for ATM classification
MONEYNESS_ATM_UPPER = 1.05              # Upper bound for ATM classification

# Volatility Analysis
IV_MINIMUM_VALID = 0.01                 # Minimum valid implied volatility
IV_TERM_STRUCTURE_MIN_OPTIONS = 10      # Minimum options for term structure analysis

# =============================================================================
# ROUNDING AND PRECISION
# =============================================================================

# Decimal Places for Output
PRICE_DECIMAL_PLACES = 2                # Decimal places for price values
PERCENTAGE_DECIMAL_PLACES = 2           # Decimal places for percentage values
PRESSURE_DECIMAL_PLACES = 4             # Decimal places for pressure calculations
GAMMA_EXPOSURE_DECIMAL_PLACES = 2       # Decimal places for gamma exposure
PIN_STRENGTH_DECIMAL_PLACES = 2         # Decimal places for pin strength

# =============================================================================
# FILE AND DIRECTORY CONFIGURATION
# =============================================================================

# Report Generation
CHART_DPI = 300                         # DPI for chart generation
CHART_FIGURE_SIZE = (12, 8)             # Default figure size for charts
PDF_PAGE_SIZE = 'letter'                # PDF page size

# Data Export
CSV_FLOAT_FORMAT = '%.4f'               # Float format for CSV exports
CSV_DATE_FORMAT = '%Y-%m-%d'            # Date format for CSV exports
